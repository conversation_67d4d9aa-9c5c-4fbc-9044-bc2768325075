package dao

import (
	"WebHome/src/database/model"
	"WebHome/src/utils"
	"encoding/json"
	"gorm.io/gorm"
)

type FavoritesFolderDao struct {
	BaseDao
	Schema *gorm.DB
}

func NewFavoritesFolderDao() *FavoritesFolderDao {
	schema := db.Table(model.NewFavoritesFolder().TableName()).Session(&gorm.Session{})
	return &FavoritesFolderDao{*baseDao, schema}
}

func (dao *FavoritesFolderDao) CreateRecord(userId int64) bool {
	favoritesFolder := model.NewFavoritesFolder()
	favoritesFolder.UserId = userId
	favoritesFolder.FavoriteFolder = "{}"
	err := dao.SingleInsert(&favoritesFolder)
	if err != nil {
		return false
	}
	return true
}

func (dao *FavoritesFolderDao) GetFavoriteFolderProfile(userId int64) []byte {
	var favoriteFolderProfile string
	conditions := "user_id = ? AND deleted_at = 0"
	values := []interface{}{userId}
	dao.Schema.Select("favorite_folder").Where(conditions, values...).Scan(&favoriteFolderProfile)
	data := make(map[string]interface{})
	_ = json.Unmarshal([]byte(favoriteFolderProfile), &data)
	result, err := utils.GzipCompressJSONData(data)
	if err != nil {
		return []byte{}
	}
	return result
}

func (dao *FavoritesFolderDao) GetOne(userId int64) model.FavoritesFolder {
	var res model.FavoritesFolder
	conditions := "user_id = ? AND deleted_at = 0"
	values := []interface{}{userId}
	dao.Schema.Where(conditions, values...).First(&res)
	return res
}
