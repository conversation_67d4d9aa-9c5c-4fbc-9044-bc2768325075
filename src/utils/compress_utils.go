package utils

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"io"
)

func GzipCompressJSONData(data map[string]interface{}) ([]byte, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return []byte{}, err
	}

	var compressedData bytes.Buffer
	gz := gzip.NewWriter(&compressedData)
	defer func(gz *gzip.Writer) {
		err := gz.Close()
		if err != nil {

		}
	}(gz)

	_, err = gz.Write(jsonData)
	if err != nil {
		return []byte{}, err
	}

	err = gz.Flush()
	if err != nil {
		return []byte{}, err
	}

	return compressedData.Bytes(), nil
}

func GzipDecompressJSONData(compressedData []byte) (map[string]interface{}, error) {
	gz, err := gzip.NewReader(bytes.NewReader(compressedData))
	if err != nil {
		return map[string]interface{}{}, err
	}

	var jsonData bytes.Buffer
	defer func(gz *gzip.Reader) {
		err := gz.Close()
		if err != nil {

		}
	}(gz)

	for {
		if _, err = jsonData.ReadFrom(gz); err != nil {
			if err == io.ErrUnexpectedEOF {
				break
			} else {
				return map[string]interface{}{}, err
			}
		}
	}

	var data map[string]interface{}
	err = json.Unmarshal(jsonData.Bytes(), &data)
	if err != nil {
		return map[string]interface{}{}, err
	}

	return data, nil
}
