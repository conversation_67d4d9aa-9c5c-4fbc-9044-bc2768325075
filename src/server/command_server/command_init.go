package command_server

import (
	redisC "WebHome/src/redis"
	"context"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/redis/go-redis/v9"
	"os"
	"path/filepath"
	"reflect"
	"runtime"
	"strings"
)

type CommandImpl interface {
	ParseCommand(parts string)
	ExecuteCommand(c *gin.Context)
}

type CommandRequest struct {
	Stdin string `json:"stdin"`
}

type BaseCommand struct {
	Options map[string]string
}

var (
	deepLAPIURL   string
	chatGPTAPIURL string
	rdb           *redis.Client
	ctx           context.Context
	commands      map[string]reflect.Type
)

func init() {
	_, file, _, _ := runtime.Caller(0)
	_ = os.Chdir(filepath.Dir(file))
	var configPath string
	if os.Getenv("ENVIRONMENT") == "local" {
		configPath = filepath.Join("..", "..", "config", ".env_local")
	} else {
		configPath = filepath.Join("..", "..", "config", ".env")
	}
	_ = godotenv.Load(configPath)

	deepLAPIURL = os.Getenv("DEEPL_URL")
	chatGPTAPIURL = os.Getenv("CHATGPT_URL")

	rdb = redisC.ConnectionRedis()
	ctx = context.Background()

	commands = map[string]reflect.Type{
		// Help
		"help": reflect.TypeOf(&HelpServer{}),

		// Man
		"man": reflect.TypeOf(&ManServer{}),

		// Translation
		"translate": reflect.TypeOf(&TranslateServer{}),
		"trans":     reflect.TypeOf(&TranslateServer{}),
		"ts":        reflect.TypeOf(&TranslateServer{}),

		// Exchange rate conversion
		"fx": reflect.TypeOf(&CurrencyConvertServer{}),
		"ex": reflect.TypeOf(&CurrencyConvertServer{}),

		// Morse code encoding
		"morse": reflect.TypeOf(&MorseServer{}),
		// Morse code decoding
		"esrom": reflect.TypeOf(&EsromServer{}),

		// Base64 conversion
		"base64": reflect.TypeOf(&Base64Server{}),
		"b64":    reflect.TypeOf(&Base64Server{}),

		// Base Conversion
		"bc": reflect.TypeOf(&BaseConversionServer{}),

		// Time to Timestamp
		"tts": reflect.TypeOf(&TimeConvertServer{}),
		// Timestamp to time
		"tst": reflect.TypeOf(&TimestampConvertServer{}),

		// Generate a password
		"genpwd": reflect.TypeOf(&GenpwdServer{}),

		// Modify username
		"rename": reflect.TypeOf(&RenameServer{}),

		// Add 3rd-party service API
		"adk": reflect.TypeOf(&AddAPIKeyServer{}),
		// Find 3rd-party service API
		"fdk": reflect.TypeOf(&FindAPIKeyServer{}),
		// Update 3rd-party service API
		"upk": reflect.TypeOf(&UpdateAPIKeyServer{}),
		// Disable 3rd-party service API
		"ban": reflect.TypeOf(&BanAPIKeyServer{}),
		// Setting up user configuration
		"set": reflect.TypeOf(&SetServer{}),

		// Modifying user privilege levels
		"chusr": reflect.TypeOf(&ChusrServer{}),

		// Switching Favorite Paths
		"cd": reflect.TypeOf(&CdServer{}),
		// View current favorite path
		"pwd": reflect.TypeOf(&PwdServer{}),
		// Show favorite content
		"ls": reflect.TypeOf(&LsServer{}),
		// Create favorite folder
		"mkdir": reflect.TypeOf(&MkdirServer{}),
		// Remove favorites or favorite records
		"rm": reflect.TypeOf(&RmServer{}),
		// Favorite a web page record
		"like": reflect.TypeOf(&LikeServer{}),
		// Rename or move favorites, favorite records
		"mv": reflect.TypeOf(&MvServer{}),
		// Modify privacy privileges
		"chmod": reflect.TypeOf(&ChmodServer{}),
		// Modify favorite URL records
		"echo": reflect.TypeOf(&EchoServer{}),

		// ChatGPT
		"gpt": reflect.TypeOf(&ChatGPTServer{}),

		// Access to the online IDE interface
		"codev": reflect.TypeOf(&CodeVServer{}),

		// Visit the blog
		"blogs": reflect.TypeOf(&BlogServer{}),

		// Open a link
		"open": reflect.TypeOf(&OpenServer{}),

		// Google search
		"google": reflect.TypeOf(&GoogleServer{}),
		"go":     reflect.TypeOf(&GoogleServer{}),

		// Bing search
		"bing": reflect.TypeOf(&BingServer{}),

		// GitHub search
		"github": reflect.TypeOf(&GitHubServer{}),
	}
}

func parseInput(input string) []string {
	var parts []string
	var buf strings.Builder

	inQuote := false
	for _, r := range input {
		if r == '\'' || r == '"' {
			if inQuote {
				parts = append(parts, strings.Trim(buf.String(), " "))
				buf.Reset()
				inQuote = false
			} else {
				inQuote = true
			}
		} else if r == ' ' {
			if !inQuote {
				if buf.Len() > 0 {
					parts = append(parts, strings.Trim(buf.String(), " "))
					buf.Reset()
				}
			} else {
				buf.WriteRune(r)
			}
		} else {
			buf.WriteRune(r)
		}
	}

	if buf.Len() > 0 {
		parts = append(parts, strings.Trim(buf.String(), " "))
	}

	return parts
}
