package command_server

import (
	"WebHome/src/database/dao"
	"WebHome/src/server/middleware"
	"WebHome/src/utils"
	"bytes"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode"

	"github.com/gin-gonic/gin"
)

type CdServer struct {
	BaseCommand
}

type LsServer struct {
	BaseCommand
}

type PwdServer struct {
	BaseCommand
}

type MkdirServer struct {
	BaseCommand
}

type RmServer struct {
	BaseCommand
}

type LikeServer struct {
	BaseCommand
}

type MvServer struct {
	BaseCommand
}

type EchoServer struct {
	BaseCommand
}

type ChmodServer struct {
	BaseCommand
}

type pathStatus byte

const (
	NoExist pathStatus = 0
	Record  pathStatus = 1
	Folder  pathStatus = 2
)

func (cs *CdServer) ParseCommand(stdin string) {
	cs.Options = make(map[string]string)
	rawParts := parseInput(stdin)
	parts := utils.RemoveElements(rawParts, "").([]string)
	switch len(parts) {
	case 0:
		cs.Options["path"] = "/"
	case 1:
		cs.Options["path"] = parts[0]
	}
}

func (cs *CdServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	userInfo := middleware.GetUserInfo(c)
	if userInfo.Username != userAuth.Username {
		c.JSON(http.StatusBadRequest, gin.H{"response": "Login expired, please <a href='/login' style='color:white'>login again</a>."})
		return
	}
	userSettings := c.MustGet("Settings")
	exprVal := userSettings.(map[string]interface{})["Expr"]
	if exprVal == nil {
		exprVal = float64(3600)
	}
	expr := int(exprVal.(float64))

	path := cs.Options["path"]
	if path == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Unsupported inputs, use the 'man cd' command for help."})
		return
	} else if path == "/" {
		userInfo.WorkingPath = path
		// Adjust cookie settings based on environment
		isLocal := os.Getenv("ENVIRONMENT") == "local"
		secure := !isLocal // Use secure cookies in production, not in local development
		c.SetCookie("userAuthorization", utils.SerializationObj(userAuth), expr, "/", "", secure, true)
		c.SetCookie("__userInfo", utils.SerializationObj(userInfo), expr, "/", "", false, false)
		c.JSON(http.StatusOK, gin.H{"response": "OK!"})
		return
	}
	absPath := utils.ResolvePath(userInfo.WorkingPath, path)
	maps := getFavoriteFolder(userAuth.UserId, expr)
	status, currentPath, _ := findMaps(absPath, "/", maps)
	if status == Folder {
		userInfo.WorkingPath = currentPath
		updateFavoriteFolderExpiration(userAuth.UserId, maps, expr)
		// Adjust cookie settings based on environment
		isLocal := os.Getenv("ENVIRONMENT") == "local"
		secure := !isLocal // Use secure cookies in production, not in local development
		c.SetCookie("userAuthorization", utils.SerializationObj(userAuth), expr, "/", "", secure, true)
		c.SetCookie("__userInfo", utils.SerializationObj(userInfo), expr, "/", "", false, false)
		c.JSON(http.StatusOK, gin.H{"response": "OK!"})
	} else {
		c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("cd: no such record or folder: %s", path)})
	}
}

func (ls *LsServer) ParseCommand(stdin string) {
	ls.Options = make(map[string]string)
	rawParts := parseInput(stdin)
	parts := utils.RemoveElements(rawParts, "").([]string)
	switch len(parts) {
	case 0:
		ls.Options["path"] = "\\"
	case 1:
		if strings.ToUpper(parts[0]) == "-L" {
			ls.Options["display"] = "list"
			ls.Options["path"] = "\\"
		} else if strings.ToUpper(parts[0]) == "-A" {
			ls.Options["all"] = "all"
			ls.Options["path"] = "\\"
		} else if strings.ToUpper(parts[0]) == "-AL" || strings.ToUpper(parts[0]) == "-LA" {
			ls.Options["all"] = "all"
			ls.Options["display"] = "list"
			ls.Options["path"] = "\\"
		} else {
			ls.Options["path"] = parts[0]
		}
	case 2:
		if strings.ToUpper(parts[0]) == "-L" {
			ls.Options["display"] = "list"
			ls.Options["path"] = parts[1]
		} else if strings.ToUpper(parts[0]) == "-A" {
			ls.Options["all"] = "all"
			ls.Options["path"] = parts[1]
		} else if strings.ToUpper(parts[0]) == "-AL" || strings.ToUpper(parts[0]) == "-LA" {
			ls.Options["all"] = "all"
			ls.Options["display"] = "list"
			ls.Options["path"] = parts[1]
		}
	}
}

func (ls *LsServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	userInfo := middleware.GetUserInfo(c)
	if userInfo.Username != userAuth.Username {
		c.JSON(http.StatusBadRequest, gin.H{"response": "Login expired, please <a href='/login' style='color:white'>login again</a>."})
		return
	}
	userSettings := c.MustGet("Settings")
	exprVal := userSettings.(map[string]interface{})["Expr"]
	if exprVal == nil {
		exprVal = float64(3600)
	}
	expr := int(exprVal.(float64))

	path := ls.Options["path"]
	if path == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Unsupported inputs, use the 'man ls' command for help."})
		return
	} else if path == "\\" {
		path = userInfo.WorkingPath
	} else {
		path = utils.ResolvePath(userInfo.WorkingPath, path)
	}
	var containsPrivateData bool
	if ls.Options["all"] != "" {
		containsPrivateData = true
	}
	maps := getFavoriteFolder(userAuth.UserId, expr)
	status, currentPath, maps := findMaps(path, "/", maps)
	if status == Folder {
		var response string
		if ls.Options["display"] == "" {
			response = generateFavoriteFolderListHTML(false, containsPrivateData, maps, currentPath)
		} else {
			response = generateFavoriteFolderListHTML(true, containsPrivateData, maps, currentPath)
		}
		c.JSON(http.StatusOK, gin.H{"response": response})
	} else {
		c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("ls: %s: No such file or directory", currentPath)})
	}
}

func (ps *PwdServer) ParseCommand(_ string) {
	return
}

func (ps *PwdServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	userInfo := middleware.GetUserInfo(c)
	if userInfo.Username != userAuth.Username {
		c.JSON(http.StatusBadRequest, gin.H{"response": "Login expired, please <a href='/login' style='color:white'>login again</a>."})
		return
	}
	c.JSON(http.StatusOK, gin.H{"response": userInfo.WorkingPath})
}

func (ms *MkdirServer) ParseCommand(stdin string) {
	ms.Options = make(map[string]string)
	rawParts := parseInput(stdin)
	parts := utils.RemoveElements(rawParts, "").([]string)
	switch len(parts) {
	case 1:
		ms.Options["directoryName"] = parts[0]
	case 2:
		if strings.ToUpper(parts[0]) != "-S" {
			return
		}
		ms.Options["isPrivate"] = "True"
		ms.Options["directoryName"] = parts[1]
	}
}

func (ms *MkdirServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	userInfo := middleware.GetUserInfo(c)
	if userInfo.Username != userAuth.Username {
		c.JSON(http.StatusBadRequest, gin.H{"response": "Login expired, please <a href='/login' style='color:white'>login again</a>."})
		return
	}
	userSettings := c.MustGet("Settings")
	exprVal := userSettings.(map[string]interface{})["Expr"]
	if exprVal == nil {
		exprVal = float64(3600)
	}
	expr := int(exprVal.(float64))

	directoryName := ms.Options["directoryName"]
	if directoryName == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Unsupported inputs, use the 'man mkdir' command for help."})
		return
	}
	path := utils.ResolvePath(userInfo.WorkingPath, directoryName)
	if regexp.MustCompile(`[!@#$%^&*()+=\[\]{};':"\\|,.<>?]`).FindString(path) != "" {
		c.JSON(http.StatusBadRequest, gin.H{"response": "ERROR: Do not use special characters for naming."})
		return
	}
	if path == "/" {
		c.JSON(http.StatusForbidden, gin.H{"response": "WARNING: Unable to operate the catalog."})
		return
	}
	maps := getFavoriteFolder(userAuth.UserId, expr)
	status, currentPath, _ := findMaps(path, "/", maps)
	if status == NoExist {
		createFavoriteFolder(path, maps, ms.Options["isPrivate"] != "")
		if savaUpdate(userAuth.UserId, maps) {
			updateFavoriteFolderExpiration(userAuth.UserId, maps, expr)
			c.JSON(http.StatusOK, gin.H{"response": "OK!"})
		} else {
			c.JSON(http.StatusServiceUnavailable, gin.H{"response": "Exception: Please try again later."})
		}
	} else if status == Record {
		c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("A record of the same name already exists: %s", currentPath)})
	} else {
		c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("A favorite of the same name already exists: %s", currentPath)})
	}
}

func (rs *RmServer) ParseCommand(stdin string) {
	rs.Options = make(map[string]string)
	rawParts := parseInput(stdin)
	parts := utils.RemoveElements(rawParts, "").([]string)
	switch len(parts) {
	case 0:
		rs.Options["directoryName"] = "\\"
	case 1:
		rs.Options["directoryName"] = parts[0]
	}
}

func (rs *RmServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	userInfo := middleware.GetUserInfo(c)
	if userInfo.Username != userAuth.Username {
		c.JSON(http.StatusBadRequest, gin.H{"response": "Login expired, please <a href='/login' style='color:white'>login again</a>."})
		return
	}
	userSettings := c.MustGet("Settings")
	exprVal := userSettings.(map[string]interface{})["Expr"]
	if exprVal == nil {
		exprVal = float64(3600)
	}
	expr := int(exprVal.(float64))

	directoryName := rs.Options["directoryName"]
	if directoryName == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Unsupported inputs, use the 'man rm' command for help."})
		return
	} else if directoryName == "\\" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Please enter the name and path of the favorite you want to remove."})
		return
	}
	path := utils.ResolvePath(userInfo.WorkingPath, directoryName)
	if strings.HasPrefix(userInfo.WorkingPath, path) {
		c.JSON(http.StatusForbidden, gin.H{"response": "ERROR: The target path cannot be operated under this path."})
		return
	}
	maps := getFavoriteFolder(userAuth.UserId, expr)
	if deleteFavoriteFolder(path, maps) {
		if savaUpdate(userAuth.UserId, maps) {
			updateFavoriteFolderExpiration(userAuth.UserId, maps, expr)
			c.JSON(http.StatusOK, gin.H{"response": "OK!"})
		} else {
			c.JSON(http.StatusServiceUnavailable, gin.H{"response": "Exception: Please try again later."})
		}
	} else {
		c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("rm: %s: No such file or directory", directoryName)})
	}
}

func (ls *LikeServer) ParseCommand(stdin string) {
	ls.Options = make(map[string]string)
	rawParts := parseInput(stdin)
	parts := utils.RemoveElements(rawParts, "").([]string)
	switch len(parts) {
	case 2:
		ls.Options["nickname"] = parts[0]
		ls.Options["path"] = parts[1]
	case 3:
		if strings.ToUpper(parts[0]) == "-S" {
			ls.Options["isPrivate"] = "true"
			ls.Options["nickname"] = parts[1]
			ls.Options["path"] = parts[2]
		} else {
			ls.Options["directory"] = parts[0]
			ls.Options["nickname"] = parts[1]
			ls.Options["path"] = parts[2]
		}
	case 4:
		if strings.ToUpper(parts[0]) != "-S" {
			return
		}
		ls.Options["isPrivate"] = "true"
		ls.Options["directory"] = parts[1]
		ls.Options["nickname"] = parts[2]
		ls.Options["path"] = parts[3]
	default:
		return
	}
}

func (ls *LikeServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	userInfo := middleware.GetUserInfo(c)
	if userInfo.Username != userAuth.Username {
		c.JSON(http.StatusBadRequest, gin.H{"response": "Login expired, please <a href='/login' style='color:white'>login again</a>."})
		return
	}
	userSettings := c.MustGet("Settings")
	exprVal := userSettings.(map[string]interface{})["Expr"]
	if exprVal == nil {
		exprVal = float64(3600)
	}
	expr := int(exprVal.(float64))

	nickname := ls.Options["nickname"]
	if nickname == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Unsupported inputs, use the 'man like' command for help"})
		return
	}
	var specialSymbols = regexp.MustCompile(`[!@#$%^&*()+=\[\]{};':"\\|,.<>/?]`)
	if specialSymbols.FindString(nickname) != "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Do not use special characters for naming."})
		return
	}
	directory := ls.Options["directory"]
	upper := userInfo.WorkingPath
	if directory != "" {
		upper = utils.ResolvePath(upper, directory)
	}
	fullPath := filepath.Join([]string{upper, nickname}...)
	urlStr := ls.Options["path"]
	parsedURL, err := url.Parse(urlStr)
	if err != nil || parsedURL.Host == "" || parsedURL.Scheme == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Unresolvable URL."})
		return
	}
	maps := getFavoriteFolder(userAuth.UserId, expr)
	status, currentPath, _ := findMaps(fullPath, "/", maps)
	if status == NoExist {
		creatFavoriteWebPage(upper, nickname, urlStr, maps, ls.Options["isPrivate"] != "")
		if savaUpdate(userAuth.UserId, maps) {
			updateFavoriteFolderExpiration(userAuth.UserId, maps, expr)
			// Adjust cookie settings based on environment
			isLocal := os.Getenv("ENVIRONMENT") == "local"
			secure := !isLocal // Use secure cookies in production, not in local development
			c.SetCookie("userAuthorization", utils.SerializationObj(userAuth), expr, "/", "", secure, true)
			c.SetCookie("__userInfo", utils.SerializationObj(userInfo), expr, "/", "", false, false)
			c.JSON(http.StatusOK, gin.H{"response": "OK!"})
		} else {
			c.JSON(http.StatusServiceUnavailable, gin.H{"response": "Exception: Please try again later."})
		}
	} else if status == Record {
		c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("A record of the same name already exists: %s", currentPath)})
	} else {
		c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("A favorite of the same name already exists: %s", currentPath)})
	}
}

func (ms *MvServer) ParseCommand(stdin string) {
	ms.Options = make(map[string]string)
	rawParts := parseInput(stdin)
	parts := utils.RemoveElements(rawParts, "").([]string)
	switch len(parts) {
	case 2:
		ms.Options["oldName"] = parts[0]
		ms.Options["newName"] = parts[1]
	}
}

func (ms *MvServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	userInfo := middleware.GetUserInfo(c)
	if userInfo.Username != userAuth.Username {
		c.JSON(http.StatusBadRequest, gin.H{"response": "Login expired, please <a href='/login' style='color:white'>login again</a>."})
		return
	}
	userSettings := c.MustGet("Settings")
	exprVal := userSettings.(map[string]interface{})["Expr"]
	if exprVal == nil {
		exprVal = float64(3600)
	}
	expr := int(exprVal.(float64))

	oldName := ms.Options["oldName"]
	newName := ms.Options["newName"]
	sourcePath := utils.ResolvePath(userInfo.WorkingPath, oldName)
	maps := getFavoriteFolder(userAuth.UserId, expr)
	sourceStatus, currentPath, _ := findMaps(sourcePath, "/", maps)
	if strings.HasPrefix(userInfo.WorkingPath, sourcePath) {
		c.JSON(http.StatusForbidden, gin.H{"response": "ERROR: The target path cannot be operated under this path."})
		return
	}
	if currentPath == sourcePath {
		if sourceStatus == Record {
			targetPath := utils.ResolvePath(userInfo.WorkingPath, newName)
			targetStatus, _, _ := findMaps(targetPath, "/", maps)
			if targetStatus == Record {
				c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("A record of the same name already exists: %s", currentPath)})
				return
			}
			recordName := filepath.Base(sourcePath)
			if targetStatus == Folder {
				targetPath = filepath.Join([]string{targetPath, recordName}...)
			}
			creatFavoriteWebPage(targetPath, recordName, "", maps, false)
			updateFavoriteFolder(sourcePath, targetPath, maps)
			if savaUpdate(userAuth.UserId, maps) {
				updateFavoriteFolderExpiration(userAuth.UserId, maps, expr)
				c.JSON(http.StatusOK, gin.H{"response": "OK!"})
			} else {
				c.JSON(http.StatusServiceUnavailable, gin.H{"response": "Exception: Please try again later."})
			}
		} else if sourceStatus == Folder {
			targetPath := utils.ResolvePath(userInfo.WorkingPath, newName)
			targetStatus, currentPath, _ := findMaps(targetPath, "/", maps)
			if targetStatus == NoExist {
				createFavoriteFolder(targetPath, maps, false)
				updateFavoriteFolder(sourcePath, targetPath, maps)
				if savaUpdate(userAuth.UserId, maps) {
					updateFavoriteFolderExpiration(userAuth.UserId, maps, expr)
					c.JSON(http.StatusOK, gin.H{"response": "OK!"})
				} else {
					c.JSON(http.StatusServiceUnavailable, gin.H{"response": "Exception: Please try again later."})
				}
			} else {
				c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("A favorite or record of the same name already exists: %s", currentPath)})
			}
		} else {
			c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("rm: %s: No such file or directory", sourcePath)})
		}
	} else {
		c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("mv: rename %s to %s: No such file or directory.", oldName, newName)})
	}
}

func (es *EchoServer) ParseCommand(stdin string) {
	es.Options = make(map[string]string)
	rawParts := strings.Split(stdin, " ")
	parts := utils.RemoveElements(rawParts, "").([]string)
	switch len(parts) {
	case 3:
		if parts[1] == ">" {
			es.Options["url"] = parts[0]
			es.Options["method"] = "0"
			es.Options["target"] = parts[2]
		} else if parts[1] == ">>" {
			es.Options["url"] = parts[0]
			es.Options["method"] = "1"
			es.Options["target"] = parts[2]
		}
	}
}

func (es *EchoServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	userInfo := middleware.GetUserInfo(c)
	if userInfo.Username != userAuth.Username {
		c.JSON(http.StatusBadRequest, gin.H{"response": "Login expired, please <a href='/login' style='color:white'>login again</a>."})
		return
	}
	userSettings := c.MustGet("Settings")
	exprVal := userSettings.(map[string]interface{})["Expr"]
	if exprVal == nil {
		exprVal = float64(3600)
	}
	expr := int(exprVal.(float64))

	urlStr := es.Options["url"]
	isAdd := es.Options["method"] == "1"
	targetPath := es.Options["target"]
	if targetPath == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Unsupported inputs, use the 'man echo' command for help."})
		return
	}
	if !isAdd {
		parsedURL, err := url.Parse(urlStr)
		if err != nil || parsedURL.Host == "" || parsedURL.Scheme == "" {
			c.JSON(http.StatusOK, gin.H{"response": "ERROR: Unresolvable URL."})
			return
		}
	}
	fullPath := utils.ResolvePath(userInfo.WorkingPath, targetPath)
	maps := getFavoriteFolder(userAuth.UserId, expr)
	status, currentPath, _ := findMaps(fullPath, "/", maps)
	if status != Record || currentPath != fullPath {
		c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("ERROR: The target path: %s doesn't exist or the path is a favorite folder", fullPath)})
		return
	}
	updateFavoriteWebPage(fullPath, urlStr, isAdd, maps)
	if savaUpdate(userAuth.UserId, maps) {
		updateFavoriteFolderExpiration(userAuth.UserId, maps, expr)
		c.JSON(http.StatusOK, gin.H{"response": "OK!"})
	} else {
		c.JSON(http.StatusServiceUnavailable, gin.H{"response": "Exception: Please try again later."})
	}
}

func (cs *ChmodServer) ParseCommand(stdin string) {
	cs.Options = make(map[string]string)
	rawParts := strings.Split(stdin, " ")
	parts := utils.RemoveElements(rawParts, "").([]string)
	if len(parts) == 1 {
		cs.Options["path"] = parts[0]
	}
}

func (cs *ChmodServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	userInfo := middleware.GetUserInfo(c)
	if userInfo.Username != userAuth.Username {
		c.JSON(http.StatusBadRequest, gin.H{"response": "Login expired, please <a href='/login' style='color:white'>login again</a>."})
		return
	}
	userSettings := c.MustGet("Settings")
	exprVal := userSettings.(map[string]interface{})["Expr"]
	if exprVal == nil {
		exprVal = float64(3600)
	}
	expr := int(exprVal.(float64))

	path := cs.Options["path"]
	if path == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: ERROR: Unsupported inputs, use the 'man chmod' command for help."})
		return
	}
	path = utils.ResolvePath(userInfo.WorkingPath, path)
	dir, nickname := filepath.Split(path)
	dir, _ = filepath.Abs(dir)
	if nickname == "" && dir == "/" {
		c.JSON(http.StatusUnauthorized, gin.H{"response": "WARNING: Disable operation of the directory!"})
		return
	}
	maps := getFavoriteFolder(userAuth.UserId, expr)
	status, currentPath, _ := findMaps(path, "/", maps)
	if status != NoExist && currentPath == path {
		modifyDisplayPermission(path, maps)
		if savaUpdate(userAuth.UserId, maps) {
			updateFavoriteFolderExpiration(userAuth.UserId, maps, expr)
			c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("OK! The %s's display permissions have been changed.", nickname)})
		} else {
			c.JSON(http.StatusServiceUnavailable, gin.H{"response": "Exception: Please try again later."})
		}
	} else {
		c.JSON(http.StatusBadRequest, gin.H{"response": "ERROR: Targets doesn't exist."})
	}
}

func getFavoriteFolder(userId int64, expr int) map[string]interface{} {
	value, err := rdb.Get(ctx, fmt.Sprintf("%d-favo", userId)).Bytes()
	if err != nil {
		value = dao.NewFavoritesFolderDao().GetFavoriteFolderProfile(userId)
		rdb.Set(ctx, fmt.Sprintf("%d-favo", userId), value, time.Duration(expr)*time.Second)
	}
	data, err := utils.GzipDecompressJSONData(value)
	if err != nil {
		return map[string]interface{}{}
	}
	return data
}

func updateFavoriteFolderExpiration(userId int64, maps map[string]interface{}, expr int) {
	favoritesFolderProfile, _ := utils.GzipCompressJSONData(maps)
	rdb.Set(ctx, fmt.Sprintf("%d-favo", userId), favoritesFolderProfile, time.Duration(expr)*time.Second)
}

func findMaps(path, currentPath string, favoriteFolderProfile map[string]interface{}) (pathStatus, string, map[string]interface{}) {
	pathParts := utils.GetPathParts(path)
	if len(pathParts) == 0 {
		return Folder, currentPath, favoriteFolderProfile
	}
	first := pathParts[0]
	if first == "." {
		return Folder, currentPath, favoriteFolderProfile
	}
	rest := strings.Join(pathParts[1:], "/")
	v, ok := favoriteFolderProfile[first]
	if !ok {
		return NoExist, currentPath, favoriteFolderProfile
	}
	if strings.HasSuffix(currentPath, "/") {
		currentPath += first
	} else {
		currentPath += "/" + first
	}
	if m, ok := v.(map[string]interface{}); ok {
		return findMaps(rest, currentPath, m)
	} else if m, ok := v.([]interface{}); ok {
		switch m[0].(type) {
		case map[string]interface{}:
			return findMaps(rest, currentPath, m[0].(map[string]interface{}))
		default:
			return Record, currentPath, favoriteFolderProfile
		}
	} else {
		return Record, currentPath, favoriteFolderProfile
	}
}

func generateFavoriteFolderListHTML(detail, containsPrivate bool, folder map[string]interface{}, currentPath string) string {
	var (
		buffer       bytes.Buffer
		folderBuffer bytes.Buffer
		recordBuffer bytes.Buffer
		folderList   []string
		recordList   []string
	)
	if detail {
		buffer.WriteString(`<table><tbody>`)
		for k, v := range folder {
			switch v.(type) {
			case map[string]interface{}:
				folderBuffer.WriteString(`<tr><td><a href="#" class="disabled-link">/`)
				folderBuffer.WriteString(k)
				folderBuffer.WriteString(`</a>`)
				folderBuffer.WriteString(`</td><td><span>`)
				folderBuffer.WriteString(filepath.Join([]string{currentPath, k}...))
				folderBuffer.WriteString(`</span></td></tr>`)
				folderList = append(folderList, folderBuffer.String())
				folderBuffer.Reset()
			case string:
				recordBuffer.WriteString(`<tr><td><a href="`)
				recordBuffer.WriteString(v.(string))
				recordBuffer.WriteString(`" class="enabled-link" target="_blank">`)
				recordBuffer.WriteString(k)
				recordBuffer.WriteString(`</a>`)
				recordBuffer.WriteString(`</td><td><span>`)
				recordBuffer.WriteString(v.(string))
				recordBuffer.WriteString(`</span></td></tr>`)
				recordList = append(recordList, recordBuffer.String())
				recordBuffer.Reset()
			case []interface{}:
				if containsPrivate {
					record := v.([]interface{})[0]
					switch record.(type) {
					case map[string]interface{}:
						folderBuffer.WriteString(`<tr><td><a href="#" class="disabled-link">/`)
						folderBuffer.WriteString(k + " *")
						folderBuffer.WriteString(`</a>`)
						folderBuffer.WriteString(`</td><td><span>`)
						folderBuffer.WriteString(filepath.Join([]string{currentPath, k}...))
						folderBuffer.WriteString(`</span></td></tr>`)
						folderList = append(folderList, folderBuffer.String())
						folderBuffer.Reset()
					case string:
						recordBuffer.WriteString(`<tr><td><a href="`)
						recordBuffer.WriteString(record.(string))
						recordBuffer.WriteString(`" class="enabled-link" target="_blank">`)
						recordBuffer.WriteString(k + " *")
						recordBuffer.WriteString(`</a>`)
						recordBuffer.WriteString(`</td><td><span>`)
						recordBuffer.WriteString(record.(string))
						recordBuffer.WriteString(`</span></td></tr>`)
						recordList = append(recordList, recordBuffer.String())
						recordBuffer.Reset()
					}
				}
			}
		}
		sort.Strings(folderList)
		for _, folder := range folderList {
			folderBuffer.WriteString(folder)
		}
		buffer.WriteString(folderBuffer.String())
		sort.Strings(recordList)
		for _, record := range recordList {
			recordBuffer.WriteString(record)
		}
		buffer.WriteString(recordBuffer.String())
		buffer.WriteString(`</tbody></table>`)
	} else {
		buffer.WriteString(`<div id="favorites_list">`)
		for k, v := range folder {
			switch v.(type) {
			case map[string]interface{}:
				folderBuffer.WriteString(`<a href="#" class="disabled-link">/`)
				folderBuffer.WriteString(k)
				folderBuffer.WriteString(`</a>`)
				folderList = append(folderList, folderBuffer.String())
				folderBuffer.Reset()
			case string:
				recordBuffer.WriteString(`<a href="`)
				recordBuffer.WriteString(v.(string))
				recordBuffer.WriteString(`" class="enabled-link" target="_blank">`)
				recordBuffer.WriteString(k)
				recordBuffer.WriteString(`</a>`)
				recordList = append(recordList, recordBuffer.String())
				recordBuffer.Reset()
			case []interface{}:
				if containsPrivate {
					record := v.([]interface{})[0]
					switch record.(type) {
					case map[string]interface{}:
						if containsPrivate {
							folderBuffer.WriteString(`<a href="#" class="disabled-link">/`)
							folderBuffer.WriteString(k + " *")
							folderBuffer.WriteString(`</a>`)
							folderList = append(folderList, folderBuffer.String())
							folderBuffer.Reset()
						}
					case string:
						if containsPrivate {
							recordBuffer.WriteString(`<a href="`)
							recordBuffer.WriteString(record.(string))
							recordBuffer.WriteString(`" class="enabled-link" target="_blank">`)
							recordBuffer.WriteString(k + " *")
							recordBuffer.WriteString(`</a>`)
							recordList = append(recordList, recordBuffer.String())
							recordBuffer.Reset()
						}
					}
				}
			}
		}
		naturalSort(folderList)
		for _, folder := range folderList {
			buffer.WriteString(folder)
		}
		naturalSort(recordList)
		for _, record := range recordList {
			buffer.WriteString(record)
		}
		buffer.WriteString(`</div>`)
	}

	return buffer.String()
}

func createFavoriteFolder(fullPath string, maps map[string]interface{}, isPrivate bool) {
	parts := utils.GetPathParts(fullPath)
	if len(parts) == 0 {
		return
	}
	currentKey := parts[0]
	currentVal, ok := maps[currentKey]
	if !ok {
		if isPrivate && len(parts) == 1 {
			currentVal = make([]map[string]interface{}, 1)
			currentVal.([]map[string]interface{})[0] = make(map[string]interface{})
		} else {
			currentVal = make(map[string]interface{})
		}
		maps[currentKey] = currentVal
		return
	}
	fullPath = "/" + strings.Join(parts[1:], "/")
	switch currentVal.(type) {
	case map[string]interface{}:
		createFavoriteFolder(fullPath, currentVal.(map[string]interface{}), isPrivate)
	case []interface{}:
		data := currentVal.([]interface{})[0]
		switch data.(type) {
		case []map[string]interface{}:
			createFavoriteFolder(fullPath, data.([]map[string]interface{})[0], isPrivate)
		case map[string]interface{}:
			createFavoriteFolder(fullPath, data.(map[string]interface{}), isPrivate)
		}
	}
}

func updateFavoriteFolder(sourcePath, targetPath string, maps map[string]interface{}) {
	var val interface{}
	sourcePathParts := utils.GetPathParts(sourcePath)
	sourceMaps := maps
	for idx, part := range sourcePathParts {
		if idx == len(sourcePathParts)-1 {
			val = sourceMaps[part]
			deleteFavoriteFolder(sourcePath, maps)
		}
		current := sourceMaps[part]
		switch current.(type) {
		case map[string]interface{}:
			sourceMaps = current.(map[string]interface{})
		case []interface{}:
			sourceMaps = current.([]interface{})[0].(map[string]interface{})
		}
	}

	setValue(val, targetPath, maps)
}

func setValue(changeVal interface{}, targetPath string, maps map[string]interface{}) {
	targetPathParts := utils.GetPathParts(targetPath)
	if len(targetPathParts) == 1 {
		maps[targetPathParts[0]] = changeVal
		return
	}
	key := targetPathParts[0]
	val := maps[key]
	targetPath = "/" + strings.Join(targetPathParts[1:], "/")
	switch val.(type) {
	case map[string]interface{}:
		setValue(changeVal, targetPath, val.(map[string]interface{}))
	case []interface{}:
		setValue(changeVal, targetPath, val.([]interface{})[0].(map[string]interface{}))
	}
}

func deleteFavoriteFolder(fullPath string, maps map[string]interface{}) bool {
	parts := utils.GetPathParts(fullPath)
	if len(parts) == 0 {
		return false
	} else if len(parts) == 1 {
		currentKey := parts[0]
		_, isMap := maps[currentKey]
		if !isMap {
			return false
		} else {
			delete(maps, currentKey)
			return true
		}
	}
	currentKey := parts[0]
	currentVal, isMap := maps[currentKey]
	if !isMap {
		return false
	}
	fullPath = "/" + strings.Join(parts[1:], "/")
	switch currentVal.(type) {
	case map[string]interface{}:
		return deleteFavoriteFolder(fullPath, currentVal.(map[string]interface{}))
	case []interface{}:
		data := currentVal.([]interface{})[0]
		switch data.(type) {
		case []map[string]interface{}:
			return deleteFavoriteFolder(fullPath, data.([]map[string]interface{})[0])
		case map[string]interface{}:
			return deleteFavoriteFolder(fullPath, data.(map[string]interface{}))
		}
	}
	return false
}

func creatFavoriteWebPage(fullPath, nickname, url string, maps map[string]interface{}, isPrivate bool) {
	parts := utils.GetPathParts(fullPath)
	if len(parts) == 0 {
		currentVal, ok := maps[nickname]
		if !ok {
			if isPrivate {
				currentVal = make([]string, 1)
				currentVal.([]string)[0] = url
			} else {
				currentVal = url
			}
			maps[nickname] = currentVal
			return
		}
	}
	currentKey := parts[0]
	_, ok := maps[currentKey]
	if !ok {
		createFavoriteFolder(fullPath, maps, false)
	}
	currentVal := maps[currentKey]
	fullPath = "/" + strings.Join(parts[1:], "/")
	switch currentVal.(type) {
	case map[string]interface{}:
		creatFavoriteWebPage(fullPath, nickname, url, currentVal.(map[string]interface{}), isPrivate)
	case []interface{}:
		data := currentVal.([]interface{})[0]
		switch data.(type) {
		case []map[string]interface{}:
			creatFavoriteWebPage(fullPath, nickname, url, data.([]map[string]interface{})[0], isPrivate)
		case map[string]interface{}:
			creatFavoriteWebPage(fullPath, nickname, url, data.(map[string]interface{}), isPrivate)
		}
	}
}

func updateFavoriteWebPage(fullPath, url string, isAdd bool, maps map[string]interface{}) {
	parts := utils.GetPathParts(fullPath)
	if len(parts) == 1 {
		val := maps[parts[0]]
		var newVal string
		switch val.(type) {
		case string:
			if isAdd {
				newVal = val.(string) + url
			} else {
				newVal = url
			}
			maps[parts[0]] = newVal
		case []interface{}:
			if isAdd {
				newVal = val.([]interface{})[0].(string) + url
			} else {
				newVal = url
			}
			maps[parts[0]] = []string{newVal}
		}
		return
	}
	key := parts[0]
	val := maps[key]
	fullPath = "/" + strings.Join(parts[1:], "/")
	switch val.(type) {
	case map[string]interface{}:
		updateFavoriteWebPage(fullPath, url, isAdd, val.(map[string]interface{}))
	case []interface{}:
		updateFavoriteWebPage(fullPath, url, isAdd, val.([]interface{})[0].(map[string]interface{}))
	}
}

func modifyDisplayPermission(fullPath string, maps map[string]interface{}) {
	parts := utils.GetPathParts(fullPath)
	if len(parts) == 1 {
		val := maps[parts[0]]
		switch val.(type) {
		case string:
			maps[parts[0]] = []string{val.(string)}
		case map[string]interface{}:
			maps[parts[0]] = []map[string]interface{}{val.(map[string]interface{})}
		case []interface{}:
			maps[parts[0]] = val.([]interface{})[0]
		}
		return
	}
	key := parts[0]
	val := maps[key]
	fullPath = "/" + strings.Join(parts[1:], "/")
	switch val.(type) {
	case map[string]interface{}:
		modifyDisplayPermission(fullPath, val.(map[string]interface{}))
	case []interface{}:
		modifyDisplayPermission(fullPath, val.([]interface{})[0].(map[string]interface{}))
	}
}

func savaUpdate(userId int64, maps map[string]interface{}) bool {
	favoriteFolderDao := dao.NewFavoritesFolderDao()
	data := favoriteFolderDao.GetOne(userId)
	if data.Id == 0 {
		return false
	}
	data.FavoriteFolder = utils.SerializationObj(maps)
	data.UpdatedAt = utils.ConvertToMilliTime(utils.GetCurrentTime())
	err := favoriteFolderDao.Save(&data)
	if err.Error != nil {
		return false
	}
	return true
}

func naturalSort(items []string) {
	sort.Slice(items, func(i, j int) bool {
		return naturalLess(items[i], items[j])
	})
}

func naturalLess(a, b string) bool {
	nameA := extractName(a)
	nameB := extractName(b)

	return compareNatural(nameA, nameB)
}

func extractName(htmlStr string) string {
	if strings.Contains(htmlStr, ">/") {
		start := strings.Index(htmlStr, ">/")
		if start != -1 {
			start += 2
			end := strings.Index(htmlStr[start:], "</a>")
			if end != -1 {
				return htmlStr[start : start+end]
			}
		}
	} else if strings.Contains(htmlStr, "\">") {
		start := strings.Index(htmlStr, "\">")
		if start != -1 {
			start += 2
			end := strings.Index(htmlStr[start:], "</a>")
			if end != -1 {
				return htmlStr[start : start+end]
			}
		}
	}
	return htmlStr
}

func compareNatural(a, b string) bool {
	a = strings.ToLower(a)
	b = strings.ToLower(b)

	i, j := 0, 0

	for i < len(a) && j < len(b) {
		if unicode.IsDigit(rune(a[i])) && unicode.IsDigit(rune(b[j])) {
			numA, nextI := extractNumber(a, i)
			numB, nextJ := extractNumber(b, j)

			if numA != numB {
				return numA < numB
			}

			i = nextI
			j = nextJ
		} else {
			if a[i] != b[j] {
				return a[i] < b[j]
			}
			i++
			j++
		}
	}

	return len(a) < len(b)
}

func extractNumber(s string, start int) (int, int) {
	end := start
	for end < len(s) && unicode.IsDigit(rune(s[end])) {
		end++
	}

	num, err := strconv.Atoi(s[start:end])
	if err != nil {
		return 0, end
	}

	return num, end
}
