package command_server

import (
	"WebHome/src/database/dao"
	"WebHome/src/database/model"
	"WebHome/src/server/middleware"
	"WebHome/src/utils"
	"errors"
	"fmt"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type RenameServer struct {
	BaseCommand
}

type AddAPIKeyServer struct {
	BaseCommand
}

type FindAPIKeyServer struct {
	BaseCommand
}

type UpdateAPIKeyServer struct {
	BaseCommand
}

type BanAPIKeyServer struct {
	BaseCommand
}

type ChusrServer struct {
	BaseCommand
}

type SetServer struct {
	BaseCommand
}

type UserSettings string

var supportServices = []model.ThirdPartyServiceName{
	model.ChatGPT,
	model.DeepL,
	model.Judge0,
}

const (
	Expr        UserSettings = "Expr"
	GPTModel    UserSettings = "GPTModel"
	Temperature UserSettings = "Temperature"
	TopP        UserSettings = "Top-P"
)

func (rs *RenameServer) ParseCommand(stdin string) {
	rs.Options = make(map[string]string)
	rawParts := strings.Split(stdin, " ")
	parts := utils.RemoveElements(rawParts, "").([]string)
	for i := 0; i < len(parts); i++ {
		arg := parts[i]
		rs.Options["rename"] += arg + " "
	}
	rs.Options["rename"] = strings.TrimRight(rs.Options["rename"], " ")
}

func (rs *RenameServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	newUserName := rs.Options["rename"]
	oldUsername := userAuth.Username
	if newUserName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"response": "ERROR: Please enter the user name to be changed."})
		return
	}
	if newUserName == oldUsername {
		c.JSON(http.StatusOK, gin.H{"response": "Username changed successfully."})
		return
	}
	ok := update3rdPartyAPIKey(oldUsername, newUserName, userAuth.UserId)
	if !ok {
		c.JSON(http.StatusOK, gin.H{"response": "Username change failed. Please try again later."})
		return
	}
	userAuth.Username = newUserName
	// Adjust cookie settings based on environment
	isLocal := os.Getenv("ENVIRONMENT") == "local"
	secure := !isLocal // Use secure cookies in production, not in local development
	c.SetCookie("userAuthorization", utils.SerializationObj(userAuth), 3600, "/", "", secure, true)
	loginInfo := map[string]string{
		"Username": newUserName,
		"IP":       c.ClientIP(),
	}
	c.SetCookie("__userInfo", utils.SerializationObj(loginInfo), 3600, "/", "", false, false)
	c.JSON(http.StatusOK, gin.H{"response": "Username changed successfully."})
}

func (adk *AddAPIKeyServer) ParseCommand(stdin string) {
	adk.Options = make(map[string]string)
	rawParts := strings.Split(stdin, " ")
	parts := utils.RemoveElements(rawParts, "").([]string)
	for i := 0; i < len(parts); i++ {
		arg := parts[i]
		if adk.Options["serviceName"] == "" {
			adk.Options["serviceName"] = arg
			continue
		}
		if adk.Options["APIKey"] == "" {
			adk.Options["APIKey"] = arg
			continue
		}
	}
}

func (adk *AddAPIKeyServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	serviceName := adk.Options["serviceName"]
	APIKey := adk.Options["APIKey"]
	if serviceName == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Please specify the API service name to be added."})
		return
	}
	isValid, thirdPartyServiceName := validationSupportService(serviceName)
	if !isValid {
		c.JSON(http.StatusBadRequest, gin.H{"response": fmt.Sprintf("ERROR: Unsupported services: %s", serviceName)})
		return
	}
	ok := saveAPIKey(userAuth.UserId, thirdPartyServiceName, APIKey, userAuth.Username)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"response": "ERROR: Failed to add, please try again later."})
		return
	}
	c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("%s Service API added successfully", thirdPartyServiceName)})
}

func (fdk *FindAPIKeyServer) ParseCommand(stdin string) {
	fdk.Options = make(map[string]string)
	rawParts := strings.Split(stdin, " ")
	parts := utils.RemoveElements(rawParts, "").([]string)
	for i := 0; i < len(parts); i++ {
		arg := parts[i]
		if fdk.Options["serviceName"] == "" {
			fdk.Options["serviceName"] = arg
			continue
		}
	}
}

func (fdk *FindAPIKeyServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	serviceName := fdk.Options["serviceName"]
	if serviceName == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Please specify the API service name to be queried."})
		return
	}
	isValid, thirdPartyServiceName := validationSupportService(serviceName)
	if !isValid {
		c.JSON(http.StatusBadRequest, gin.H{"response": fmt.Sprintf("ERROR: Unsupported services: %s", serviceName)})
		return
	}
	APIKey := findAPIKey(userAuth.UserId, thirdPartyServiceName)
	if APIKey == "" {
		c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("ERROR: Failed to query the available %s service corresponding API key.\nPlease use the \"adk\" command to add or \"upk\" command to update to available.", serviceName)})
		return
	}
	decryptValue := utils.DecryptCipherText(APIKey, userAuth.Username)
	c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("The API key for the %s service is: %s", serviceName, decryptValue)})
}

func (upk *UpdateAPIKeyServer) ParseCommand(stdin string) {
	upk.Options = make(map[string]string)
	rawParts := strings.Split(stdin, " ")
	parts := utils.RemoveElements(rawParts, "").([]string)
	for i := 0; i < len(parts); i++ {
		arg := parts[i]
		if upk.Options["serviceName"] == "" {
			upk.Options["serviceName"] = arg
			continue
		}
		if upk.Options["APIKey"] == "" {
			upk.Options["APIKey"] = arg
			continue
		}
	}
}

func (upk *UpdateAPIKeyServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	serviceName := upk.Options["serviceName"]
	APIKey := upk.Options["APIKey"]
	if serviceName == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Please specify the API service name to be updated."})
		return
	}
	isValid, thirdPartyServiceName := validationSupportService(serviceName)
	if !isValid {
		c.JSON(http.StatusBadRequest, gin.H{"response": fmt.Sprintf("ERROR: Unsupported 3rd-party services: %s", serviceName)})
		return
	}
	ok := updateAPIKey(userAuth.UserId, thirdPartyServiceName, APIKey, userAuth.Username)
	if !ok {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Cannot find the service, please use the \"adk\" command to add the service."})
		return
	}
	c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("%s Service API updated successfully", thirdPartyServiceName)})
}

func (ban *BanAPIKeyServer) ParseCommand(stdin string) {
	ban.Options = make(map[string]string)
	rawParts := strings.Split(stdin, " ")
	parts := utils.RemoveElements(rawParts, "").([]string)
	for i := 0; i < len(parts); i++ {
		arg := parts[i]
		if ban.Options["serviceName"] == "" {
			ban.Options["serviceName"] = arg
			continue
		}
		if ban.Options["APIKey"] == "" {
			ban.Options["APIKey"] = arg
			continue
		}
	}
}

func (ban *BanAPIKeyServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	serviceName := ban.Options["serviceName"]
	if serviceName == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Please specify the API service name to be updated."})
		return
	}
	isValid, thirdPartyServiceName := validationSupportService(serviceName)
	if !isValid {
		c.JSON(http.StatusBadRequest, gin.H{"response": fmt.Sprintf("ERROR: Unsupported services: %s", serviceName)})
		return
	}
	ok := disableAPIKey(userAuth.UserId, thirdPartyServiceName)
	if !ok {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Cannot find the service, please use the adk command to add the service."})
		return
	}
	c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("%s Service API disabled successfully", thirdPartyServiceName)})
}

func (cs *ChusrServer) ParseCommand(stdin string) {
	cs.Options = make(map[string]string)
	rawParts := strings.Split(stdin, " ")
	parts := utils.RemoveElements(rawParts, "").([]string)
	for i := 0; i < len(parts); i++ {
		arg := parts[i]
		if cs.Options["targetUser"] == "" {
			cs.Options["targetUser"] = arg
			continue
		}
		if cs.Options["chmod"] == "" {
			cs.Options["chmod"] = arg
			continue
		}
	}
}

func (cs *ChusrServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	targetUser := cs.Options["targetUser"]
	if targetUser == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Unknown target audience."})
		return
	}
	chmod := cs.Options["chmod"]
	if chmod == "" {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Unknown privilege."})
		return
	}
	isValid, shift, direction := validationChmod(chmod)
	if !isValid {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Unknown privilege."})
		return
	}
	userEntityDao := dao.NewUserEntityDao()
	user := userEntityDao.IsUserExists(targetUser)
	operatorId, _ := userEntityDao.SearchUserId(userAuth.Username)
	operator := userEntityDao.FindUserByUserId(operatorId)
	if user.Id == 0 {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Unknown target audience."})
		return
	}
	if operator.Role < model.Admin {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Insufficient operating privileges."})
		return
	}
	targetRole := user.Role
	isValid, newRole := validationAuth(operator.Role, targetRole, shift, direction)
	if !isValid {
		c.JSON(http.StatusOK, gin.H{"response": "ERROR: Insufficient operating privileges."})
		return
	}
	user.Role = newRole
	userEntityDao.Updates(user)
	c.JSON(http.StatusOK, gin.H{"response": "Successfully!"})
}

func (ss *SetServer) ParseCommand(stdin string) {
	ss.Options = make(map[string]string)
	rawParts := strings.Split(stdin, " ")
	parts := utils.RemoveElements(rawParts, "").([]string)
	switch len(parts) {
	case 2:
		ss.Options["key"] = parts[0]
		ss.Options["val"] = parts[1]
	}
}

func (ss *SetServer) ExecuteCommand(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"response": "ERROR: This command needs to be used while logged in."})
		return
	}
	key := ss.Options["key"]
	val := ss.Options["val"]
	res, err := validateSupportUserSetting(key, val)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{"response": fmt.Sprintf("Error: %s.", err.Error())})
		return
	}
	userSettings := getUserSettings(userAuth.UserId)
	userSettings[string(res[0].(UserSettings))] = res[1]
	updateUserSettings(userAuth.UserId, userSettings)
	c.JSON(http.StatusOK, gin.H{"response": "OK!"})
}

func update3rdPartyAPIKey(oldName, newName string, userId int64) bool {
	var flag bool
	userEntityDao := dao.NewUserEntityDao()
	users := userEntityDao.FindUserListByUserId([]int64{userId})
	if len(users) == 0 {
		return false
	}
	user := users[0]
	userAPIKeyDao := dao.NewUserAPIKeyDao()
	userAPIKeyModels := userAPIKeyDao.GetUserAllAPIKeys(userId)
	tx := userAPIKeyDao.DB.Begin()
	for i := 0; i < 3; i++ {
		for _, userAPIKeyModel := range userAPIKeyModels {
			APIKey := userAPIKeyModel.APIKey
			updatedAt := userAPIKeyModel.UpdatedAt
			decryptValue := utils.DecryptCipherText(APIKey, oldName)
			userAPIKeyModel.APIKey = utils.EncryptPlainText([]byte(decryptValue), newName)
			userAPIKeyModel.UpdatedAt = utils.ConvertToMilliTime(utils.GetCurrentTime())
			if err := tx.Updates(userAPIKeyModel).Error; err != nil {
				flag = false
				tx.Rollback()
				userAPIKeyModel.APIKey = APIKey
				userAPIKeyModel.UpdatedAt = updatedAt
				break
			}
		}
		user.Username = newName
		updatedAt := user.UpdatedAt
		user.UpdatedAt = utils.ConvertToMilliTime(utils.GetCurrentTime())
		if err := tx.Updates(user).Error; err != nil {
			flag = false
			tx.Rollback()
			user.Username = oldName
			user.UpdatedAt = updatedAt
		} else {
			flag = true
			tx.Commit()
			break
		}
	}
	return flag
}

func validationSupportService(serviceName string) (bool, model.ThirdPartyServiceName) {
	for _, supportService := range supportServices {
		if strings.EqualFold(string(supportService), serviceName) {
			return true, supportService
		}
	}
	return false, ""
}

func saveAPIKey(userId int64, serviceName model.ThirdPartyServiceName, APIKey, username string) bool {
	var ok bool
	userAPIKeyDao := dao.NewUserAPIKeyDao()
	entity := userAPIKeyDao.GetAPIKey(userId, serviceName, false)
	if entity.Id != 0 {
		entity.APIKey = utils.EncryptPlainText([]byte(APIKey), username)
		entity.IsEnabled = true
		entity.UpdatedAt = utils.ConvertToMilliTime(utils.GetCurrentTime())
		userAPIKeyDao.Updates(entity)
		return true
	}
	for i := 0; i < 3; i++ {
		ok = userAPIKeyDao.CreateUserAPIKey(userId, serviceName, APIKey, username)
		if ok {
			return ok
		}
	}
	return ok
}

func findAPIKey(userId int64, serviceName model.ThirdPartyServiceName) string {
	userAPIKeyDao := dao.NewUserAPIKeyDao()
	entity := userAPIKeyDao.GetAPIKey(userId, serviceName, true)
	return entity.APIKey
}

func updateAPIKey(userId int64, serviceName model.ThirdPartyServiceName, APIKey, username string) bool {
	userAPIKeyDao := dao.NewUserAPIKeyDao()
	entity := userAPIKeyDao.GetAPIKey(userId, serviceName, false)
	if entity.Id != 0 {
		entity.APIKey = utils.EncryptPlainText([]byte(APIKey), username)
		entity.IsEnabled = true
		entity.UpdatedAt = utils.ConvertToMilliTime(utils.GetCurrentTime())
		err := userAPIKeyDao.SingleUpdate(entity)
		if err != nil {
			return false
		}
		return true
	}
	return false
}

func disableAPIKey(userId int64, serviceName model.ThirdPartyServiceName) bool {
	userAPIKeyDao := dao.NewUserAPIKeyDao()
	entity := userAPIKeyDao.GetAPIKey(userId, serviceName, true)
	if entity.Id != 0 {
		entity.IsEnabled = false
		entity.UpdatedAt = utils.ConvertToMilliTime(utils.GetCurrentTime())
		err := userAPIKeyDao.SingleUpdate(entity)
		if err != nil {
			return false
		}
		return true
	}
	return false
}

func validationChmod(chmod string) (bool, int, string) {
	pattern := "^[\\+\\-]+$"
	re := regexp.MustCompile(pattern)
	if re.MatchString(chmod) {
		leftCount := strings.Count(chmod, "+")
		rightCount := strings.Count(chmod, "-")
		if leftCount > rightCount {
			return true, leftCount - rightCount, "<<"
		} else {
			return true, rightCount - leftCount, ">>"
		}
	} else {
		return false, 0, ""
	}
}

func validationAuth(operator, target model.UserRole, shift int, direction string) (bool, model.UserRole) {
	if operator <= target {
		return false, target
	}
	var newRole model.UserRole
	if direction == "<<" {
		newRole = target << shift
	} else {
		newRole = target >> shift
	}
	if newRole < model.Client {
		newRole = model.Client
	} else if operator <= newRole {
		newRole = operator >> 1
	}
	return true, newRole
}

func validateSupportUserSetting(key, val string) ([]interface{}, error) {
	var supportUserSetting = []UserSettings{
		Expr,
		GPTModel,
		Temperature,
		TopP,
	}
	var GPTModelList = []string{
		"gpt-3.5-turbo",
		"gpt-3.5-turbo-16k",
		"gpt-4",
		"gpt-4-32k",
	}
	for _, supportKey := range supportUserSetting {
		if strings.EqualFold(string(supportKey), key) {
			switch supportKey {
			case Expr:
				num, err := strconv.Atoi(val)
				if err != nil {
					return []interface{}{}, errors.New("unexpected expiration settings")
				}
				if num < 600 || num > 86400 {
					return []interface{}{}, errors.New("the expiration time (in s) setting should be between 10 minutes (600) and 24 hours (86400)")
				}
				return []interface{}{Expr, num}, nil
			case GPTModel:
				for _, gptModel := range GPTModelList {
					if strings.EqualFold(gptModel, val) {
						return []interface{}{GPTModel, gptModel}, nil
					}
				}
				return []interface{}{}, errors.New("unsupported GPT model")
			case Temperature:
				f, err := strconv.ParseFloat(val, 64)
				if err != nil {
					return []interface{}{}, errors.New("unexpected Temperature input")
				}
				if f >= 0 && f <= 2 {
					return []interface{}{Temperature, f}, nil
				} else {
					return []interface{}{}, errors.New("the input value for Temperature should be between 0 and 2")
				}
			case TopP:
				f, err := strconv.ParseFloat(val, 64)
				if err != nil {
					return []interface{}{}, errors.New("unexpected Top-P input")
				}
				if f >= 0 && f <= 1 {
					return []interface{}{TopP, f}, nil
				} else {
					return []interface{}{}, errors.New("the input value for Top-P should be between 0 and 1")
				}
			}
		}
	}
	return []interface{}{}, errors.New("unsupported inputs, use the 'man set' command for help")
}

func getUserSettings(userId int64) map[string]interface{} {
	value, err := rdb.Get(ctx, fmt.Sprintf("%d-set", userId)).Bytes()
	if err != nil {
		return map[string]interface{}{}
	}
	data, err := utils.GzipDecompressJSONData(value)
	if err != nil {
		return map[string]interface{}{}
	}
	return data
}

func updateUserSettings(userId int64, userSettings map[string]interface{}) {
	favoritesFolderProfile, _ := utils.GzipCompressJSONData(userSettings)
	rdb.Set(ctx, fmt.Sprintf("%d-set", userId), favoritesFolderProfile, 0)
}
