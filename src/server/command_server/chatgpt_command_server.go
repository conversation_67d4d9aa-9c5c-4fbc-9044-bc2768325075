package command_server

import (
	"WebHome/src/database/dao"
	"WebHome/src/database/model"
	"WebHome/src/server/middleware"
	"WebHome/src/utils"
	"bytes"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"io"
	"net/http"
)

type ChatGPTServer struct {
	BaseCommand
}

type chatResponse struct {
	Error   errorMsg               `json:"error"`
	ID      string                 `json:"id"`
	Object  string                 `json:"object"`
	Created int64                  `json:"created"`
	Model   string                 `json:"model"`
	Choices []chatCompletionChoice `json:"choices"`
	Usage   usage                  `json:"usage"`
}

type errorMsg struct {
	Message string      `json:"message"`
	Type    string      `json:"type"`
	Param   interface{} `json:"param"`
	Code    string      `json:"code"`
}

type chatCompletionChoice struct {
	Index        int                   `json:"index"`
	Message      ChatCompletionMessage `json:"message"`
	FinishReason FinishReason          `json:"finish_reason"`
}

type usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

type ChatCompletionMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type FinishReason string

func (cs *ChatGPTServer) ParseCommand(stdin string) {
	cs.Options = make(map[string]string)
	cs.Options["message"] = stdin
}

func (cs *ChatGPTServer) ExecuteCommand(c *gin.Context) {
	ok, res := getChatGPTAPIKey(c)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"response": res})
		return
	}
	userSettings := c.MustGet("Settings")
	gptModelVal := userSettings.(map[string]interface{})["GPTModel"]
	if gptModelVal == nil {
		gptModelVal = "gpt-3.5-turbo"
	}
	gptModel := gptModelVal.(string)
	temperatureVal := userSettings.(map[string]interface{})["Temperature"]
	if temperatureVal == nil {
		temperatureVal = float64(0)
	}
	temperature := temperatureVal.(float64)
	topPVal := userSettings.(map[string]interface{})["Top-P"]
	if topPVal == nil {
		topPVal = float64(1)
	}
	topP := topPVal.(float64)
	req, _ := generateReq(res, gptModel, cs.Options["message"], temperature, topP)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"response": "Service exception, please try again later."})
		return
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {

		}
	}(resp.Body)
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"response": "Service exception, please try again later."})
		return
	}
	var parsedData chatResponse
	_ = json.Unmarshal(responseData, &parsedData)
	if parsedData.Error.Message == "" {
		c.JSON(http.StatusOK, gin.H{"response": parsedData.Choices[0].Message.Content})
	} else {
		c.JSON(http.StatusOK, gin.H{"response": parsedData.Error.Message})
	}
}

func getChatGPTAPIKey(c *gin.Context) (bool, string) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId == 0 {
		return false, "ERROR: This command needs to be used while logged in."
	}
	key := getUserChatGPTAPIKey(userAuth)
	if key == "" {
		return false, "ERROR: Please set a valid API Key for the user."
	}
	return true, key
}

func getUserChatGPTAPIKey(userAuth middleware.UserAuth) string {
	userApiKeyDao := dao.NewUserAPIKeyDao()
	entity := userApiKeyDao.GetAPIKey(userAuth.UserId, model.ChatGPT, true)
	if entity.IsEnabled == false {
		return ""
	}
	key := utils.DecryptCipherText(entity.APIKey, userAuth.Username)
	return key
}

func generateReq(token, model, message string, temperature, topP float64) (*http.Request, error) {
	requestData, _ := json.Marshal(map[string]interface{}{
		"model": model,
		"messages": []map[string]string{
			{
				"role":    "user",
				"content": message,
			},
		},
		"temperature": temperature,
		"top_p":       topP,
	})
	req, err := http.NewRequest(http.MethodPost, chatGPTAPIURL, bytes.NewBuffer(requestData))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")
	return req, nil
}
