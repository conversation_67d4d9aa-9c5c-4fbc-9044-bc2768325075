package middleware

import (
	redisC "WebHome/src/redis"
	"context"
	"github.com/joho/godotenv"
	"github.com/redis/go-redis/v9"
	"os"
	"path/filepath"
	"runtime"
)

var (
	rdb *redis.Client
	ctx context.Context
)

func init() {
	_, file, _, _ := runtime.Caller(0)
	_ = os.Chdir(filepath.Dir(file))
	var configPath string
	if os.Getenv("ENVIRONMENT") == "local" {
		configPath = filepath.Join("..", "..", "config", ".env_local")
	} else {
		configPath = filepath.Join("..", "..", "config", ".env")
	}
	_ = godotenv.Load(configPath)

	rdb = redisC.ConnectionRedis()
	ctx = context.Background()
}
