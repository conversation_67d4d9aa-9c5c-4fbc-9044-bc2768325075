package middleware

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
)

type UserAuth struct {
	UserId   int64  `json:"uid"`
	Username string `json:"username"`
	Role     string `json:"role"`
}

type UserInfo struct {
	Username    string `json:"username"`
	IP          string `json:"IP"`
	WorkingPath string `json:"workingPath"`
}

func UpdateAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		userAuth, err := c.<PERSON>("userAuthorization")
		userInfo, err := c.<PERSON>("__userInfo")
		if err != nil || userAuth == "" || userInfo == "" {
			c.Next()
			return
		}
		userSettings, exist := c.Get("Settings")
		if !exist {
			c.Next()
			return
		}
		exprVal := userSettings.(map[string]interface{})["Expr"]
		if exprVal == nil {
			exprVal = float64(3600)
		}
		expr := int(exprVal.(float64))
		c.<PERSON><PERSON><PERSON><PERSON>("userAuthorization", userAuth, expr, "/", "", true, true)
		c.<PERSON>("__userInfo", userInfo, expr, "/", "", false, false)
		c.Next()
	}
}

func GetUserAuth(c *gin.Context) (userAuth UserAuth) {
	authToken, err := c.Cookie("userAuthorization")
	if err != nil {
		return
	}
	if err := json.Unmarshal([]byte(authToken), &userAuth); err != nil {
		return
	}
	return
}

func GetUserInfo(c *gin.Context) (UserInfo UserInfo) {
	userInfo, err := c.Cookie("__userInfo")
	if err != nil {
		return
	}
	if err := json.Unmarshal([]byte(userInfo), &UserInfo); err != nil {
		return
	}
	return
}
