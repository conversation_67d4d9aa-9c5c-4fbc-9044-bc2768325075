package middleware

import (
	"WebHome/src/utils"
	"fmt"
	"github.com/gin-gonic/gin"
)

func GetUserSettings() gin.HandlerFunc {
	return func(c *gin.Context) {
		userAuth := GetUserAuth(c)
		if userAuth.UserId == 0 {
			c.Next()
			return
		}
		value, err := rdb.Get(ctx, fmt.Sprintf("%d-set", userAuth.UserId)).Bytes()
		if err != nil {
			c.Set("Settings", map[string]interface{}{})
			c.Next()
			return
		}
		data, err := utils.GzipDecompressJSONData(value)
		if err != nil {
			c.Set("Settings", data)
			c.Next()
			return
		}
		c.Set("Settings", data)
	}
}
