package server

import (
	"WebHome/src/server/middleware"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
)

func LogoutHandle(c *gin.Context) {
	userAuth := middleware.GetUserAuth(c)
	if userAuth.UserId != 0 {
		c.<PERSON><PERSON>("__userInfo", "", -1, "/", "", false, true)
		c.<PERSON>("userAuthorization", "", -1, "/", "", true, true)
		rdb.Del(ctx, fmt.Sprintf("%d-favo", userAuth.UserId))
		c.<PERSON>(http.StatusOK, gin.H{"response": true})
	} else {
		c.<PERSON>(http.StatusUnauthorized, gin.H{"response": false})
	}
}
