function getCookie(name) {
    let cookieArr = document.cookie.split(";");

    for (let i = 0; i < cookieArr.length; i++) {
        let cookiePair = cookieArr[i].trim().split("=");
        let cookieName = cookiePair[0];
        let cookieValue = cookiePair[1];

        if (cookieName === name) {
            return decodeURIComponent(cookieValue);
        }
    }
    return null;
}

function includesIgnoreCase(stringList, string) {
    const stringListUpper = stringList.map(str => str.toUpperCase());
    return stringListUpper.includes(string.toUpperCase())
}

async function postRequestServer(url, body) {
    return await fetch(url, {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        credentials: 'include',
        body: body
    })
}

let commandInput;
let currentLayoutMode = 0;

function updateLayout(mode) {
    currentLayoutMode = mode;
    const mainContainer = document.getElementById('mainContainer');
    if (!mainContainer) return;

    mainContainer.className = mainContainer.className.replace(/layout-mode-\d+/g, '');
    mainContainer.classList.add('main-container', `layout-mode-${mode}`);
    updateButtonIcon(mode);
}

function updateButtonIcon(mode) {
    const layoutGrid = document.querySelector('.layout-grid');
    if (!layoutGrid) return;

    layoutGrid.className = layoutGrid.className.replace(/layout-mode-\d+-icon/g, '');
    layoutGrid.classList.add('layout-grid', `layout-mode-${mode}-icon`);
    layoutGrid.innerHTML = '';

    switch (mode) {
        case 0:
            layoutGrid.innerHTML = '<div class="grid-item active"></div>';
            break;
        case 1:
            layoutGrid.innerHTML = `
                <div class="grid-item active"></div>
                <div class="grid-item active"></div>
                <div class="grid-item active"></div>
            `;
            break;
    }
}

function initLayoutToggle() {
    const layoutBtn = document.getElementById('layoutToggleBtn');
    if (!layoutBtn) return;

    layoutBtn.onclick = function (e) {
        e.preventDefault();
        e.stopPropagation();
        currentLayoutMode = currentLayoutMode === 0 ? 1 : 0;
        updateLayout(currentLayoutMode);
        return false;
    };

    updateLayout(0);
}

function initializeAll() {
    commandInput = document.querySelector('.command__input');
    if (!commandInput) return;

    initLayoutToggle();
    bindCommandInputEvents();
}



function bindCommandInputEvents() {
    if (!commandInput) return;

    commandInput.addEventListener("compositionstart", (event) => {
        event.preventDefault();
        isComposing = true;
    });

    commandInput.addEventListener("compositionupdate", (event) => {
        event.preventDefault();
        isComposing = true;
    });

    commandInput.addEventListener("compositionend", (event) => {
        event.preventDefault();
        isComposing = false;
    });

    commandInput.addEventListener("keydown", (event) => {
        if (event.key === "Enter") {
            const command = commandInput.value
            commandHistory.push(command)
            historyIndex = commandHistory.length - 1
            commandInput.setAttribute("disabled", "disabled");
            commandListener(event);
            event.preventDefault();
        } else if (event.key === "ArrowUp" && !isComposing) {
            event.preventDefault();
            if (historyIndex >= 0) {
                commandInput.value = commandHistory[historyIndex];
                if (historyIndex > 0) {
                    historyIndex--;
                }
            }
        } else if (event.key === "ArrowDown" && !isComposing) {
            event.preventDefault();
            if (historyIndex < commandHistory.length - 1) {
                historyIndex++;
                commandInput.value = commandHistory[historyIndex];
            } else {
                commandInput.value = "";
                historyIndex = commandHistory.length - 1
            }
        }
    });
}

document.addEventListener('DOMContentLoaded', function () {
    setTimeout(initializeAll, 100);
});

if (document.readyState !== 'loading') {
    setTimeout(initializeAll, 100);
}

const commandHistory = []
let historyIndex = -1;
let isComposing = false;

async function addNewTerminal(username, ip) {
    const terminal = document.querySelector(".terminal");
    terminal.classList.add("terminal");
    const newCommand = document.createElement("div");
    newCommand.classList.add("command");
    const newUserInfo = document.createElement("span");
    newUserInfo.classList.add("user__info");
    newUserInfo.textContent = username + "@" + ip + "\u00A0/\u00A0$\u00A0";
    const newCommandInput = document.createElement("input");
    newCommandInput.classList.add("command__input");
    newCommandInput.type = "text";
    newCommandInput.autofocus = true;
    newCommandInput.addEventListener("compositionstart", (event) => {
        isComposing = true;
    });

    newCommandInput.addEventListener("compositionupdate", (event) => {
        isComposing = true;
    });

    newCommandInput.addEventListener("compositionend", (event) => {
        isComposing = false;
    });
    newCommandInput.addEventListener("keydown", (event) => {
        if (event.key === "Enter") {
            const command = newCommandInput.value
            commandHistory.push(command)
            historyIndex = commandHistory.length - 1
            newCommandInput.setAttribute("disabled", "disabled");
            commandListener(event);
            event.preventDefault();
        } else if (event.key === "ArrowUp" && !isComposing) {
            event.preventDefault();
            if (historyIndex >= 0) {
                newCommandInput.value = commandHistory[historyIndex];
                if (historyIndex > 0) {
                    historyIndex--;
                }
            }
        } else if (event.key === "ArrowDown" && !isComposing) {
            event.preventDefault();
            if (historyIndex < commandHistory.length - 1) {
                historyIndex++;
                newCommandInput.value = commandHistory[historyIndex];
            } else {
                newCommandInput.value = "";
                historyIndex = commandHistory.length - 1
            }
        }
    });
    newCommand.appendChild(newUserInfo);
    newCommand.appendChild(newCommandInput);
    terminal.appendChild(newCommand);
    newCommandInput.focus();
}

class Command {
    constructor(commandName) {
        this.commandName = commandName;
        this.aliasList = [];
        this.params = {};
        this.resp = undefined;
    };

    alias(aliasName) {
        this.aliasList.add(aliasName);
    };

    async execute(inputText) {
        var body = JSON.stringify({ "stdin": inputText })
        var responseObj = await this.postRequestServer("/command", body)
        if (responseObj.redirected) {
            window.location.href = responseObj.url;
        }
        var response = await responseObj.json()
        if (responseObj.status === 302) {
            var urlString = response["response"]
            var url = new URL(urlString);
            var mode = url.searchParams.get('mode');
            if (mode === "newTab") {
                url.searchParams.delete("mode");
                urlString = url.toString()
                window.open(urlString, "_blank");
                return
            } else {
                window.location.href = urlString;
                return
            }
        }
        this.resp = response["response"]
        this.printResponse()
    }

    async postRequestServer(url, body) {
        return await fetch(url, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            credentials: 'include',
            body: body
        })
    };

    printResponse() {
        if (this.resp !== "undefined") {
            const currentTerminalElement = document.querySelector(".terminal")
            const result = document.createElement("div");
            result.classList.add("result");
            result.innerHTML = this.resp;
            currentTerminalElement.appendChild(result);
        }
    }
}

class ClearCommand extends Command {
    constructor(commandName) {
        super(commandName);
        this.aliasList = ["clear", "cls"];
    }

    async execute(inputList) {
        if (inputList.length !== 0) {
            this.resp = "ERROR: [command] command that cannot be parsed".replace("[command]", "[" + this.commandName + " " + inputList.join(" ") + "]");
            this.printResponse();
        } else {
            await this.thanosSnap();
        }
    }

    async thanosSnap() {
        const terminal = document.querySelector(".terminal");
        const terminalRect = terminal.getBoundingClientRect();
        
        // 添加清屏状态类
        terminal.classList.add('thanos-clearing');
        
        // 获取所有文本元素，按从下到上排序
        const textElements = Array.from(terminal.querySelectorAll('.command, .result'));
        const particles = [];
        
        // 按Y坐标从下到上排序元素
        textElements.sort((a, b) => {
            const rectA = a.getBoundingClientRect();
            const rectB = b.getBoundingClientRect();
            return rectB.top - rectA.top; // 从下到上
        });
        
        // 从下到上逐行处理
        textElements.forEach((element, index) => {
            setTimeout(() => {
                // 添加消散类
                element.classList.add('thanos-element', 'dissolving');
                
                // 创建细腻的粒子效果
                const rect = element.getBoundingClientRect();
                const text = element.textContent || element.innerText;
                
                // 大量细小粒子，像灰尘一样
                const particleCount = Math.min(Math.max(text.length * 4, 50), 200);
                
                for (let i = 0; i < particleCount; i++) {
                    setTimeout(() => {
                        const particle = document.createElement('div');
                        particle.className = 'thanos-particle';
                        
                        // 粒子位置更贴近文字边缘
                        const x = rect.left - terminalRect.left + Math.random() * rect.width;
                        const y = rect.top - terminalRect.top + Math.random() * rect.height;
                        
                        particle.style.left = x + 'px';
                        particle.style.top = y + 'px';
                        
                        // 灰尘被风从左到右吹散的效果
                        const driftX = Math.random() * 35 + 15; // 向右飘散
                        const driftY = (Math.random() - 0.5) * 15; // 轻微上下浮动
                        const finalX = driftX + Math.random() * 50 + 25; // 继续向右
                        const finalY = driftY + (Math.random() - 0.5) * 25; // 继续轻微浮动
                        
                        // 随机灰色调，模拟不同的灰尘颗粒
                        const grayValue = Math.floor(Math.random() * 60 + 160);
                        particle.style.background = `rgba(${grayValue}, ${grayValue}, ${grayValue}, 0.5)`;
                        
                        particle.style.setProperty('--drift-x', driftX + 'px');
                        particle.style.setProperty('--drift-y', driftY + 'px');
                        particle.style.setProperty('--final-x', finalX + 'px');
                        particle.style.setProperty('--final-y', finalY + 'px');
                        
                        terminal.appendChild(particle);
                        particles.push(particle);
                        
                        // 立即开始粒子动画
                        particle.classList.add('fading');
                    }, Math.random() * 80); // 粒子出现时间随机化
                }
            }, index * 60); // 从下到上，每行间隔60ms
        });
        
        // 清理和重置
        setTimeout(() => {
            terminal.innerHTML = "";
            terminal.classList.remove('thanos-clearing');
            
            // 清理所有粒子
            particles.forEach(particle => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            });
            
            // 创建新的终端输入
            const userInfo = JSON.parse(getCookie("__userInfo"));
            let username, ip;
            
            if (userInfo == null) {
                username = "Visitor";
                ip = "127.0.0.1";
            } else {
                username = userInfo["username"];
                ip = userInfo["IP"];
            }
            
            addNewTerminal(username, ip);
        }, textElements.length * 60 + 400);
    }
}

class LoginCommand extends Command {
    constructor(commandName) {
        super(commandName);
        this.aliasList = ["login"]
    }

    async execute(inputList) {
        if (inputList.length !== 0) {
            this.resp = "ERROR: [command] command that cannot be parsed".replace("[command]", "[" + this.commandName + " " + inputList.join(" ") + "]");
        } else {
            window.location.href = "/login";
            return
        }
        if (typeof this.resp !== "undefined") {
            this.printResponse();
        }
    }
}

class ExitCommand extends Command {
    constructor(commandName) {
        super(commandName);
        this.aliasList = ["logout", "exit"];
    }

    async execute(inputList) {
        const response = await postRequestServer("/logout", null);
        const data = await response.json();
        this.resp = data["response"];
        if (this.commandName.toLowerCase() === "logout" && this.resp === true) {
            this.resp = "Logout Successful!"
            this.printResponse();
        } else if (this.commandName.toLowerCase() === "logout" && this.resp === false) {
            this.resp = "ERROR: No login info."
            this.printResponse();
        } else if (this.commandName.toLowerCase() === "exit" && this.resp === true) {
            this.resp = "Logout Successful!"
            this.printResponse();
        } else if (this.commandName.toLowerCase() === "exit" && this.resp === false) {
            window.open('', '_self').close();
        }

    }
}

class RegisterCommand extends Command {
    constructor(commandName) {
        super(commandName);
        this.aliasList = ["register", "signup"]
    }

    async execute(inputList) {
        if (inputList.length !== 0) {
            this.resp = "ERROR: [command] command that cannot be parsed".replace("[command]", "[" + this.commandName + " " + inputList.join(" ") + "]");
        } else {
            window.location.href = "/register";
            return
        }
        if (typeof this.resp !== "undefined") {
            this.printResponse();
        }
    }
}

async function commandListener(event) {
    const inputText = event.target.value.trim();
    const textList = inputText.split(' ')
    const command = textList[0];

    const commandClasses = [
        ClearCommand, LoginCommand, ExitCommand, RegisterCommand
    ];
    let commandClass;
    for (const cls of commandClasses) {
        const commandInstance = new cls(command);
        if (includesIgnoreCase(commandInstance.aliasList, command)) {
            commandClass = commandInstance;
        }
    }
    if (typeof commandClass === "undefined") {
        commandClass = new Command(command);
        await commandClass.execute(inputText)
    } else {
        await commandClass.execute(textList.slice(1));
    }

    const userInfo = JSON.parse(getCookie("__userInfo"));
    let username, ip;

    if (userInfo == null) {
        username = "Visitor";
        ip = "127.0.0.1";
    } else {
        username = userInfo["username"];
        ip = userInfo["IP"]
    }
    await addNewTerminal(username, ip);
}
