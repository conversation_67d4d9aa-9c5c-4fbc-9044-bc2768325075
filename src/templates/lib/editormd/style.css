* {
    padding: 0;
    margin: 0;
}

*, *:before, *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td,hr,button,article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{
    margin: 0;
    padding: 0;
}

article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section, summary {
    display: block;
}

audio, canvas, video {
    display: inline-block;
}

img {
    border: none;
    vertical-align: middle;
}

.clear {
    *zoom: 1;
}

.clear:before, .clear:after {
    height: 0;
    content: "";
    font-size: 0;
    display: table;
    line-height: 0;
    visibility: hidden;
}

.clear:after {
    clear: both;
}

body {
    font-size: 14px;
    color: #666;
    font-family: "Microsoft YaHei", "微软雅黑", Helvetica, Tahom<PERSON>, STX<PERSON><PERSON>, "华文细黑", STHeiti, "Helvetica Neue", Helvetica, Tahoma, "Droid Sans", "wenquanyi micro hei", FreeSans, Arimo, Arial, SimSun, "宋体", Heiti, "黑体", sans-serif;
    background: #fff;
    text-align: center;
}

#layout {
    text-align: left;
}

#layout > header, .btns {
    padding: 15px 0;
    width: 90%;
    margin: 0 auto;
}

.btns {
    padding-top: 0;
}

.btns button {
    padding: 2px 8px;
}

#layout > header > h1 {
    font-size: 20px;
    margin-bottom: 10px;
}

.btns button, .btn {
    padding: 8px 10px;
    background: #fff;
    border: 1px solid #ddd;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    cursor: pointer;
    -webkit-transition: background 300ms ease-out;
    transition: background 300ms ease-out;
}

.btns button:hover, .btn:hover {
    background: #f6f6f6;
}

#title-box {
    font-size: 32px;
}

/*.transparent-textbox {*/
/*    position: relative;*/
/*    background-color: transparent;*/
/*    outline: none;*/
/*    width: 300px;*/
/*    height: 50px;*/
/*    font-size: 32px;*/
/*    color: #0f0f0f;*/
/*}*/

#nameModal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(128, 128, 128, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

#name {
    position: relative;
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
}

.close-button {
    position: absolute;
    top: 1px;
    left: 1px;
    width: 20px;
    height: 20px;
    background-image: url('../../close-icon.png');
    background-size: cover;
    cursor: pointer;
}

.form-input {
    position: relative;
    width: 100%;
    height: 50px;
    margin-top: 20px;
    margin-bottom: 20px;
}

.form-input input {
    width: 100%;
    height: 100%;
    padding: 5px;
    border: 1px solid #ccc;
    box-sizing: border-box;
}

.form-input .input-label {
    font-family: '得意黑', system-ui;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    color: gray;
    background-color: #fff;
    margin-left: 5px;
    padding: 0 5px;
    transition: all 0.1s;
}

.form-input input:focus + .input-label {
    position: absolute;
    top: 0;
    transform: translateY(-100%);
    font-size: 16px;
    color: gray;
    background-color: transparent;
    padding-left: 0;
    padding-bottom: 5px;
}

.form-output {
    width: 100%;
    display: flex;
    justify-content: center;
}

input[type="submit"] {
    background-color: #EE3F4D;
    color: #fff;
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
}

input[type="submit"]:hover {
    background-color: #002FA7;
}