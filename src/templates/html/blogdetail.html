{{ template "header" . }}

<link rel="stylesheet" href="/templates/lib/editormd/editormd.css">
<style>
    .markdown-body{
        background-color: transparent;
    }
    body {
        justify-content: center;
        display: flex;
    }
    #content {
        width: 80%;
    }
    #content::-webkit-scrollbar {
        width: 0.5em;
        height: 0.5em;
    }
    #content::-webkit-scrollbar-track {
        background-color: transparent;
    }
    img {
        width: 400px;
        height: 300px;
        cursor: pointer;
        display: block;
        margin: 0 auto;
    }
</style>

{{ template "middle" . }}

{{if .edit}}
<div id="editable" style="display: flex"><a href="/blogs/edit/{{.title}}" methods="get">Edit</a></div>
{{else}}
<div id="unEditable" style="display: none"><a href="/blogs/edit/{{.title}}" methods="get">Edit</a></div>
{{end}}

<div id="content">
    <textarea id="append-test" style="display:none;">{{ .content }}</textarea>
</div>

<script src="/templates/js/JQuery.js"></script>
<script src="/templates/lib/editormd/lib/marked.min.js"></script>
<script src="/templates/lib/editormd/lib/prettify.min.js"></script>
<script src="/templates/lib/editormd/lib/raphael.min.js"></script>
<script src="/templates/lib/editormd/lib/underscore.min.js"></script>
<script src="/templates/lib/editormd/lib/sequence-diagram.min.js"></script>
<script src="/templates/lib/editormd/lib/flowchart.min.js"></script>
<script src="/templates/lib/editormd/lib/jquery.flowchart.min.js"></script>
<script src="/templates/lib/editormd/editormd.js"></script>
<script type="text/javascript">
    $(function () {
        editormd.markdownToHTML("content", {
            htmlDecode: "style,script,iframe",
            emoji: true,
            taskList: true,
            tex: true,
            flowChart: true,
            sequenceDiagram: true,
        });

        window.onload = function () {
            const images = document.querySelectorAll('img');
            images.forEach(function(image) {
                image.style.transform = "scale(1.0)";
                image.addEventListener("click", function () {
                    if (image.style.transform === "scale(1)") {
                        image.style.transform = "scale(1.5)";
                    } else {
                        image.style.transform = "scale(1)";
                    }
                })
            })
        }
    });
</script>

{{ template "footer" . }}