body {
    background-color: #1c1c1c;
    color: #f1f1f1;
    margin: 0;
    padding: 0;
    overflow: hidden;
}


* {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

*::-webkit-scrollbar {
    width: 0px;
    height: 0px;
    background: transparent;
}

*::-webkit-scrollbar-track {
    background: transparent;
}

*::-webkit-scrollbar-thumb {
    background: transparent;
}


.main-container {
    height: 100vh;
    padding: 8px;
    box-sizing: border-box;
}


.layout-toggle-btn {
    position: fixed;
    top: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    user-select: none;
    z-index: 9999;
}

.layout-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.15);
    transform: scale(1.05);
}

.layout-toggle-btn:active {
    background: rgba(255, 255, 255, 0.03);
    transform: scale(0.95);
}

.layout-icon {
    width: 16px;
    height: 16px;
}

.layout-grid {
    display: grid;
    gap: 1px;
    width: 100%;
    height: 100%;
}

.grid-item {
    background-color: rgba(255, 255, 255, 0.4);
    border-radius: 2px;
    transition: background-color 0.2s ease;
}


.layout-mode-0-icon {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
}

.layout-mode-1-icon {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
}

.layout-mode-1-icon .grid-item:nth-child(1) {
    grid-row: 1 / 3;
}



.grid-item.active {
    background-color: rgba(255, 255, 255, 0.8);
}

.grid-item.inactive {
    background-color: rgba(255, 255, 255, 0.2);
}


.terminal {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow-y: auto;
    padding: 16px;
    box-sizing: border-box;
}

.command {
    display: flex;
    align-items: center;
}

.user__info {
    display: inline-block;
    white-space: nowrap;
    margin-left: 6px;
}

.command__input {
    font-family: '得意黑', system-ui;
    color: #f1f1f1;
    background-color: transparent;
    border: none;
    font-size: 16px;
    width: 100%;
    outline: none;
}

.command__input::placeholder {
    color: #aaa;
}

.command__input:focus {
    outline: none;
}

.result {
    margin-left: 6px;
    white-space: normal;
}


.notice,
.search {
    display: none;
}


.layout-mode-0 {
    display: flex;
    gap: 8px;
}

.layout-mode-0 .terminal {
    flex: 1;
    height: calc(100vh - 16px);
}

.layout-mode-0 .right-panels {
    display: none;
    flex: 1;
}




.layout-mode-1 {
    display: flex;
    gap: 8px;
}

.layout-mode-1 .terminal {
    flex: 1;
    height: calc(100vh - 16px);
    background: rgba(255, 255, 255, 0.002);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
}

.layout-mode-1 .right-panels {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 8px;
}

.layout-mode-1 .notice {
    display: flex;
    flex-direction: column;
    flex: 1;
    background: rgba(255, 255, 255, 0.002);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    padding: 16px;
    box-sizing: border-box;
    overflow-y: auto;
}

.layout-mode-1 .search {
    display: flex;
    flex-direction: column;
    flex: 1;
    background: rgba(255, 255, 255, 0.002);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    padding: 16px;
    box-sizing: border-box;
    overflow-y: auto;
}




a:link {
    color: black;
}

a:visited {
    color: black;
}

a:hover {
    color: red;
}

a:active {
    color: green;
}

table {
    border-collapse: collapse;
    padding: 0;
    word-break: initial;
}

table tr {
    margin: 0;
    padding: 0;
}

table td {
    margin: 0;
    padding: 1px 13px;
}

table td:first-child {
    vertical-align: top;
    margin-top: 0;
}

table td:last-child {
    margin-bottom: 0;
}

.manBox {
    width: 50%;
    height: auto;
    border: none;
    display: flex;
    flex-direction: column;
    max-height: 40vh;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 16px;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.divider {
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 2px solid #ccc;
    margin-bottom: 15px;
}

.divider-line {
    flex-grow: 1;
    border: none;
    height: 2px;
}

.divider-text {
    padding: 0 10px;
}

.title {
    margin-top: 6px;
    font-weight: bold;
}

.result .title:first-child {
    margin-top: 0;
}

.commandName,
.aliasList,
.usage,
.desc,
.instruction {
    text-indent: 2em;
}

.optionality {
    font-style: italic;
}

span {
    white-space: normal;
}

code {
    display: block;
    text-indent: 2em;
    white-space: normal;
}

li {
    white-space: normal;
}

.optionTable {
    border: solid white;
    text-align: left;
}

tr span.instruction:nth-child(n+2) {
    text-indent: 2ch;
    display: inline-block;
}

#favorites_list {
    display: flex;
    flex-wrap: wrap;
    margin: -5px;
}

.disabled-link {
    flex-basis: 16%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-decoration: none;
    margin-left: 6px;
    pointer-events: none;
    color: #168168 !important;
}

.enabled-link {
    flex-basis: 16%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-decoration: none;
    margin-left: 6px;
    color: white !important;
}

.disabled-link:last-child,
.enabled-link:last-child {
    margin-bottom: 3px;
}

/* 灭霸响指效果 */
.thanos-particle {
    position: absolute;
    width: 0.3px;
    height: 0.3px;
    background: rgba(200, 200, 200, 0.6);
    pointer-events: none;
    z-index: 10000;
    border-radius: 50%;
}

.thanos-particle.fading {
    animation: thanosDisintegrate 0.6s ease-out forwards;
}

@keyframes thanosDisintegrate {
    0% {
        opacity: 0.7;
        transform: scale(1) translateX(0);
    }
    30% {
        opacity: 0.5;
        transform: scale(0.8) translateX(var(--drift-x)) translateY(var(--drift-y));
    }
    70% {
        opacity: 0.2;
        transform: scale(0.4) translateX(var(--final-x)) translateY(var(--final-y));
    }
    100% {
        opacity: 0;
        transform: scale(0.1) translateX(var(--final-x)) translateY(var(--final-y));
    }
}

.terminal.thanos-clearing {
    position: relative;
    overflow: hidden;
}

.thanos-element {
    position: relative;
    overflow: hidden;
}

.thanos-element.dissolving {
    animation: elementDissolve 0.5s ease-out forwards;
}

@keyframes elementDissolve {
    0% {
        opacity: 1;
        filter: blur(0px);
        transform: scale(1);
    }
    60% {
        opacity: 0.4;
        filter: blur(1px);
        transform: scale(0.96);
    }
    100% {
        opacity: 0;
        filter: blur(2px);
        transform: scale(0.92);
    }
}