body {
    background-color: #1a1a1a;
    color: #fff;
    font-family: Arial, sans-serif;
}

.container {
    width: 400px;
    margin: 0 auto;
    padding: 50px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

h1 {
    font-family: '得意黑', system-ui;
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 30px;
    text-align: center;
}

form {
    display: flex;
    flex-direction: column;
}

input {
    border-radius: 5px;
}

input[type="submit"] {
    background-color: #EE3F4D;
    color: #fff;
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
}

input[type="submit"]:hover {
    background-color: #002FA7;
}

.password-meter {
    display: flex;
    width: 100%;
    height: 3px;
    background-color: lightgray;
    margin-top: 5px;
}

.password-meter-bar {
    flex-grow: 1;
    height: 100%;
    transition: background-color 0.1s;
}

.password-meter-bar--weak {
    background-color: red;
}

.password-meter-bar--medium {
    background-color: yellow;
}

.password-meter-bar--strong {
    background-color: limegreen;
}

.error-message {
    font-size: 14px;
    color: red;
}

.form-container {
    position: relative;
    width: 400px;
    margin: 0 auto;
}

.form-input {
    position: relative;
    width: 100%;
    height: 50px;
    margin-top: 20px;
    margin-bottom: 20px;
}

.form-input input {
    width: 100%;
    height: 100%;
    padding: 5px;
    border: 1px solid #ccc;
    box-sizing: border-box;
}

.form-input .input-label {
    font-family: '得意黑', system-ui;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    color: gray;
    background-color: #fff;
    margin-left: 5px;
    padding: 0 5px;
    transition: all 0.1s;
}

.form-input input:focus + .input-label {
    position: absolute;
    top: 0;
    transform: translateY(-100%);
    font-size: 16px;
    color: gray;
    background-color: transparent;
    padding-left: 0;
    padding-bottom: 5px;
}

.input-focused .input-label {
    top: 0;
    transform: translateY(-100%);
    margin-left: 0;
    padding-left: 0;
    padding-bottom: 5px;
    background-color: transparent;
}

.form-output {
    width: 100%;
    display: flex;
    justify-content: center;
}

.account-exists {
    font-family: '得意黑', system-ui;
    text-align: center;
    margin-top: 10px;
}

.visitor {
    font-family: '得意黑', system-ui;
    text-align: center;
    margin-top: 10px;
}

.no-underline {
    text-decoration: none;
}

#verificationModal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(128, 128, 128, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

#verificationCode {
    position: relative;
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
}

.close-button {
    position: absolute;
    top: 1px;
    left: 1px;
    width: 20px;
    height: 20px;
    background-image: url('../close-icon.png');
    background-size: cover;
    cursor: pointer;
}
