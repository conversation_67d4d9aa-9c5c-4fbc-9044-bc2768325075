body {
    background-color: #1a1a1a;
    color: #fff;
    font-family: Arial, sans-serif;
}

.container {
    width: 400px;
    margin: 0 auto;
    padding: 50px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.5);
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

h1 {
    font-family: '得意黑', system-ui;
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 30px;
    text-align: center;
}

form {
    display: flex;
    flex-direction: column;
}

label {
    font-size: 18px;
    margin-bottom: 10px;
}

input[type="text"],
input[type="password"] {
    padding: 10px;
    margin-bottom: 20px;
    border-radius: 5px;
    border: none;
}

input[type="submit"] {
    background-color: #EE3F4D;
    color: #fff;
    padding: 10px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
}

input[type="submit"]:hover {
    background-color: #002FA7;
}