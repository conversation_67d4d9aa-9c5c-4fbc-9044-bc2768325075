#blog-navigation {
    display: flex;
    align-content: center;
    justify-content: flex-end;
    position: fixed;
    top: 0;
    width: 100%;
    border-radius: 0;
    margin: 0;
    background: #1e1e1e;
    border-bottom: 1px solid rgba(255,255,255,.08);
    padding: 2px;
    height: 45px
}

#save-btn {
    position: absolute;
    width: 88px;
    height: 90%;
    right: 5%;
    transform: translateY(0%);
    background-color: #60a3b5;
    margin-top: 1px;
    margin-bottom: -1px;
    border: 0;
    border-radius: 6px;
}

#save-btn:disabled {
    cursor: not-allowed;
}

#blog-title {
    display: flex;
    align-content: center;
    justify-content: flex-end;
    position: absolute;
    width: 20%;
    left: 1%;
    height: 90%;
    font-size: 32px;
    margin-top: 1px;
    margin-bottom: -1px;
}

#editor-md {
    position: fixed;
    top: 45px;
    padding-bottom: 3%;
    background-color: black;
}

.titleName {
    position: relative;
    height: 90%;
    width: 100%;
    padding-left: 6px;
    margin-top: 2px;
    border: 0;
    border-radius: 6px;
}

#classification {
    position: absolute;
    height: 90%;
    width: 20%;
    left: 22%;
    padding-left: 6px;
    margin-top: 2px;
    padding-bottom: 1px;
    border: 0;
    border-radius: 6px;
}

#dropdown {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 6px;
}

.switch {
    display: flex;
    align-content: center;
    justify-content: flex-end;
    position: absolute;
    margin-top: 3px;
    left: 44%;
    width: 60px;
    height: 35px;
}

.switch input[type="checkbox"] {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input[type="checkbox"]:checked + .slider {
    background-color: #000000;
}

input[type="checkbox"]:checked + .slider:before {
    transform: translateX(26px);
}

.label {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 10px;
    color: #888;
    font-size: 14px;
}

#layout {
    width: 80%;
}