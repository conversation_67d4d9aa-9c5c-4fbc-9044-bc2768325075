version: "3"

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5466:5466"
    depends_on:
      - mysql
      - redis

  mysql:
    image: ${MYSQL_IMAGE}
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PWD}
      # MYSQL_USER: set your db user
      # MYSQL_PASSWORD: set your password
      MYSQL_DATABASE: geek_home
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "3300:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7.0
    container_name: redis
    restart: always
    ports:
      - "6370:6379"
    volumes:
      - redis_data:/var/lib/redis

volumes:
  mysql_data:
  redis_data:
