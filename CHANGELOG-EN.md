### Beat-1.2.0
+ Release Time

  2023/09/11

+ Release Notes
  + User rights control added
  + Favorites system is online
  + Blog list paging function optimized
  + Logging middleware functionality is complete
  + Actions performed while logged in will refresh the offline time

+ Development Project
  + Back-office management functions (expected to be online in the official version)
  + CodeV front-end interface optimization (Beat-1.3.0 expected to go live)
  + CodeV standard input functionality (expected to go live with Beat-1.3.0)
  + alias command renaming feature (Plans to remove this feature)
  + rm command and mv command functionality optimization (expected to be live in Beat-1.4.0)
  + Blog editing and reading experience optimization

### Beat-1.1.0
+ Release Time

  2023/08/15

+ Release Notes
  + Optimized and added some command functions, fixed some bugs
  + Optimize blog editing and reading experience
  + Add database privilege control
  + Project deployed [plac3bo.dev](https://plac3bo.dev)

+ Development Project
  + Back-office management functions (expected to be online in the official version)
  + CodeV front-end interface optimization (Beat-1.2.0 expected to go live)
  + CodeV standard input functionality (expected to go live with Beat-1.2.0)
  + alias command renaming feature (expected to go live in Beat-1.2.0)
  + User permission adding function (expected to go live in Beat-1.2.0)
  + Blog editing and reading experience optimization

### Beat-1.0.0
+ Release Time

  2023/08/04

+ Release
  + Command functionality is now available
  + Blog functionality is now available
  + CodeV online IDE is now available
  + Login and Registration are now available

+ Todo
  + Back-office management functions (expected to be online in the official version)
  + CodeV front-end interface optimization (Beat-1.2.0 expected to go live)
  + CodeV standard input functionality (expected to go live with Beat-1.2.0)
  + alias command renaming feature (expected to go live in Beat-1.2.0)