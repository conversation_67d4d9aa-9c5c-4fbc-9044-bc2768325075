### Beat-1.2.0
+ 更新时间
  2023/09/11
+ 更新说明
  + 用户权限控制已添加
  + 收藏系统上线
  + 博客列表分页功能优化
  + 日志中间件功能完成
  + 在登录状态进行操作会刷新下线时间
+ 开发计划
  + 后台管理功能（预计正式版本上线）
  + CodeV前端界面优化（预计Beat-1.3.0上线）
  + CodeV标准输入功能（预计Beat-1.3.0上线）
  + alias命令重命名功能（计划取消该功能）
  + rm命令及mv命令功能优化（预计Beat-1.4.0上线）
  + 博客编辑和阅读体验优化

### Beat-1.1.0
+ 更新时间
  2023/08/15
+ 更新说明
  + 优化和新增了部分命令功能，修复了一些Bug
  + 优化博客编辑和阅读体验
  + 新增数据库权限控制
  + 项目已部署 [plac3bo.dev](https://plac3bo.dev)
+ 开发计划
  + 后台管理功能（预计正式版本上线）
  + CodeV前端界面优化（预计Beat-1.2.0上线）
  + CodeV标准输入功能（预计Beat-1.2.0上线）
  + alias命令重命名功能（预计Beat-1.2.0上线）
  + 用户权限添加功能（预计Beat-1.2.0上线）
  + 博客编辑和阅读体验优化

### Beat-1.0.0
+ 更新时间
  2023/08/04
+ 更新说明
  + 命令功能上线
  + 博客功能上线
  + CodeV在线IDE功能上线
  + 登录注册功能上线
+ 开发计划
  + 后台管理功能（预计正式版本上线）
  + CodeV前端界面优化（预计Beat-1.2.0上线）
  + CodeV输入功能（预计Beat-1.2.0上线）
  + alias命令重命名功能（预计Beat-1.2.0上线）